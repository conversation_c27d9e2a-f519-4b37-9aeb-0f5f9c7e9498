package com.logictrue.interfaces.service.excel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.logictrue.common.core.utils.StringUtils;
import com.logictrue.interfaces.domain.dto.DataSetDto;
import com.logictrue.interfaces.domain.dto.OriginalDataDto;
import com.logictrue.interfaces.domain.vo.ReportLenVO;
import com.logictrue.interfaces.domain.vo.PreviewVO;
import com.logictrue.interfaces.entity.dto.ReportParams;
import com.logictrue.interfaces.strategy.param.ParamContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.awt.*;
import java.util.*;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Service(value = "dynamicCellService")
public class DynamicCellService {

    private static final Logger logger = LoggerFactory.getLogger(DynamicCellService.class);

    @Autowired
    private BorderService borderService;
    @Autowired
    private ColumnAndRowService columnAndRowService;

    /**
     * 处理动态数据单元格自动扩展
     *
     * @param dataSet 数据
     * @param v
     * @param cellStr
     * @param cnt
     * @param r
     * @param c
     * @param merge
     * @param dbObject
     * @param colAddCntMap
     */
    public void handleDynamicCellObject(DataSetDto dataSet, PreviewVO previewVO, String v, String cellStr,
                                        int cnt, int rnt, int r, int c,
                                        JSONObject merge, JSONObject dbObject,
                                        Map<Integer, Integer> colAddCntMap,
                                        Map<Integer, Integer> rowAddCntMap,
                                        Map<Integer, List<ReportLenVO>> rowLenMap) {
        //处理登录用户 请求时间动态参数
        //定义参数为#{param.user}  #{param.start}
        if (v.contains("#{param.")) {
            //设置单元格高度
            columnAndRowService.setRowLen(dbObject, r, c, cnt, 0);
            Map<String, String> paramMap = previewVO.getParamMap();
            //borderSet(cnt,rnt,r,c,dbObject,0);
            String prefix = "param.";
            String suffix = "}";
            /*int start = v.indexOf(prefix);
            int end = v.lastIndexOf(suffix);
            String paramName = v.substring(start + prefix.length(), end);*/

            String[] values = v.split("#\\{");

            StringBuilder result = new StringBuilder(values[0]);

            for (int i = 1; i < values.length; i++) {
                String temp = values[i];
                int last = temp.lastIndexOf(suffix);

                String value = temp.substring(prefix.length(), last);

                String replaceValue = new ParamContext(value, paramMap, dbObject).getResult();
                //设置默认值  replaceValue可能为空格
                if (replaceValue != null) {
                    replaceValue = temp.replace(prefix + value + suffix, replaceValue);
                } else {
                    //无替换值 原样返回
                    replaceValue = temp;
                }

                result.append(replaceValue);
            }

            JSONObject addCellData = JSONObject.parseObject(cellStr);
            addCellData.put("r", r + cnt);
            addCellData.put("c", c + rnt);
            addCellData.getJSONObject("v").put("v", result.toString());
            addCellData.getJSONObject("v").put("m", result.toString());

            //是否是合并单元格
            JSONObject mc = addCellData.getJSONObject("v").getJSONObject("mc");
            if (mc != null) {
                mc.put("r", addCellData.getInteger("r"));
                initCellMerge(merge, mc);
            }
            borderService.borderRangeSet(cnt, rnt, r, c, dbObject, 0);
            ReportUtils.setCalc(addCellData, dbObject);
            dbObject.getJSONArray("celldata").add(addCellData);
            return;
        }

        Map<String, OriginalDataDto> originalDataMap = (Map<String, OriginalDataDto>) previewVO.getResults();
        if (Objects.isNull(originalDataMap)) {
            return;
        }
        OriginalDataDto originalDataDto = originalDataMap.get(dataSet.getSetCode());
        if (originalDataDto == null) {
            return;
        }

        List<Object> cellDynamicData = originalDataDto.getData();
        if (cellDynamicData != null && !cellDynamicData.isEmpty()) {
            Object cellData = cellDynamicData.get(0);
            JSONObject cellJObj = new JSONObject();
            if (cellData instanceof JSONObject) {
                cellJObj = (JSONObject) cellData;
            } else if (cellData instanceof Map) {
                cellJObj = JSON.parseObject(JSON.toJSONString(cellData));
            }
            if (v.contains(".merge.")) {
                //设置单元格高度
                columnAndRowService.setRowLen(dbObject, r, c, cnt, 0);
                // v格式 #{6f1a2cd158d84c498745dd0caba31f27.merge.sum_data::flbm#2}
                String[] data = v.split("\\.");
                String mergeName = "";
                String mergeType = "";
                if (data.length > 2) {
                    String[] mergeSplit = data[2].split("::");
                    mergeName = mergeSplit[0];
                    mergeType = mergeSplit[1].substring(0, mergeSplit[1].length() - 1);
                }

                String[] opera = mergeType.split("#");
                String filed = opera[0];
                JSONArray mergeData = cellJObj.getJSONArray(mergeName);

                Map<String, List<Object>> sort = new LinkedHashMap<>();

                if ("all".equals(opera[1])) {
                    String tempStr = "";
                    int count = 0;
                    //遍历 获取需要合并的数据
                    for (int i = 0; i < mergeData.size(); i++) {
                        JSONObject datum = mergeData.getJSONObject(i);
                        String operaStr = datum.getString(filed);
                        String operaStr2 = operaStr + "###-" + count;
                        if (sort.containsKey(operaStr)) {
                            if (operaStr.equals(tempStr)) {
                                sort.get(operaStr).add(datum);
                            }else if(operaStr2.equals(tempStr)){
                                sort.get(operaStr2).add(datum);
                            } else {
                                count++;
                                List<Object> mergeList = new LinkedList<>();
                                mergeList.add(datum);
                                operaStr = operaStr+"###-"+count;
                                sort.put(operaStr, mergeList);
                            }
                        } else {
                            List<Object> mergeList = new ArrayList<>();
                            mergeList.add(datum);
                            sort.put(operaStr, mergeList);
                        }
                        columnAndRowService.setRowLen(dbObject, r, c, cnt, i);
                        tempStr = operaStr;
                    }

                    JSONArray newMergeData = new JSONArray();
                    AtomicInteger addRow = new AtomicInteger();

                    final int[] a = {0};
                    sort.forEach((s, objects) -> {
                        JSONObject addMergeCell = JSONObject.parseObject(cellStr);

                        //获取合并单元格
                        JSONObject mc = addMergeCell.getJSONObject("v").getJSONObject("mc");
                        //设置合并单元格
                        if (mc != null) {
                            mc.put("r", r + a[0]);
                            mc.put("c", c);
                            mc.put("rs", objects.size());
                        }else {
                            mc = new JSONObject();
                            mc.put("r", r + a[0]);
                            mc.put("c", c);
                            mc.put("rs", objects.size());
                            mc.put("cs", 1);
                        }

                        //设置行号 列号不变
                        addMergeCell.put("r", cnt + r + a[0]); //行数增加
                        String cellValue = s;
                        int index = 0;
                        if ((index = s.indexOf("###-"))!= -1) {
                            cellValue = s.substring(0, index);
                        }
                        addMergeCell.getJSONObject("v").put("v", cellValue);
                        addMergeCell.getJSONObject("v").put("m", cellValue);


                        if (objects.size() > 1) {
                            addMergeCell.getJSONObject("v").put("mc", mc);
                            //合并单元格需要处理config.merge
                            merge.put(mc.getString("r") + "_" + mc.getString("c"), mc);
                        }
                        ReportUtils.setCalc(addMergeCell, dbObject);
                        dbObject.getJSONArray("celldata").add(addMergeCell);
                        //设置边框
                        borderAddSet(r, c, a[0], rnt, objects.size() - 1, 0, dbObject, 1, 1);

                        newMergeData.addAll(objects);
                        addRow.addAndGet(objects.size());
                        //累计占用的单元格
                        a[0] += objects.size();
                    });
                    cellJObj.put(mergeName, newMergeData);

                    //进行合并单元格操作  设置占用的长度  设置合并单元格之后的值
                    colAddCntMap.put(c, addRow.get() - 1);

                }else if (opera[1].startsWith("%")) {//列表模式纵向指点合并单元格
                    Integer num = Integer.parseInt(opera[1].substring(1));
                    mergeAppointNum(dbObject,cellJObj,mergeData,filed,cellStr, cnt,  rnt,  r,  c,colAddCntMap, mergeName, merge,  num);
                }else {

                    Integer operaLen = Integer.parseInt(opera[1]);


                    //遍历 获取需要合并的数据
                    for (int i = 0; i < mergeData.size(); i++) {
                        JSONObject datum = mergeData.getJSONObject(i);
                        String operaStr = datum.getString(filed);
                        String mergeContent = operaStr.substring(0, operaLen);
                        String newName = operaStr.substring(operaLen);
                        if ("总计".equals(operaStr) || "合计".equals(operaStr)) {
                            mergeContent = operaStr;
                            newName = operaStr;
                        }

                        datum.put(filed, newName);
                        if (sort.containsKey(mergeContent)) {
                            sort.get(mergeContent).add(datum);
                        } else {
                            List<Object> mergeList = new ArrayList<>();
                            mergeList.add(datum);
                            sort.put(mergeContent, mergeList);
                        }
                    }
                    JSONArray newMergeData = new JSONArray();
                    AtomicInteger addRow = new AtomicInteger();

                    final int[] a = {0};
                    sort.forEach((s, objects) -> {
                        JSONObject addMergeCell = JSONObject.parseObject(cellStr);
                        //设置行号 列号不变
                        addMergeCell.put("r", cnt + r + a[0]); //行数增加
                        addMergeCell.getJSONObject("v").put("v", s);
                        addMergeCell.getJSONObject("v").put("m", s);
                        //设置合并单元格
                        JSONObject mc = new JSONObject();
                        mc.put("r", r + a[0]);
                        mc.put("c", c);
                        mc.put("rs", objects.size());
                        mc.put("cs", 1);
                        //合计和总计横向合并
                        if ("合计".equals(s) || "总计".equals(s)) {
                            mc.put("rs", 1);
                            mc.put("cs", 2);
                        }

                        if (objects.size() > 1) {
                            addMergeCell.getJSONObject("v").put("mc", mc);
                            //合并单元格需要处理config.merge
                            merge.put(mc.getString("r") + "_" + mc.getString("c"), mc);
                        }
                        ReportUtils.setCalc(addMergeCell, dbObject);
                        dbObject.getJSONArray("celldata").add(addMergeCell);
                        //设置边框
                        borderAddSet(r, c, a[0], rnt, objects.size() - 1, 0, dbObject, 1, 1);

                        newMergeData.addAll(objects);
                        addRow.addAndGet(objects.size());
                        //累计占用的单元格
                        a[0] += objects.size();
                    });
                    cellJObj.put(mergeName, newMergeData);

                    //进行合并单元格操作  设置占用的长度  设置合并单元格之后的值
                    colAddCntMap.put(c, addRow.get() - 1);
                }
            } else if (cellJObj.containsKey("info")) {

                String fullName = dataSet.getFieldLabel();
                String one = "";
                String name = "";
                String[] border = fullName.split("##");
                if (border.length > 1) {
                    one = border[0];
                    name = border[1];
                } else {
                    name = fullName.substring(fullName.indexOf(".") + 1);
                    one = fullName.substring(0, fullName.indexOf("."));
                }
                Object o = cellJObj.get(one);

                String prefix = "::";
                String suffix = "}";
                int start = name.indexOf(prefix);
                String model = null;
                String pName = null;
                String patten = null;//时间格式
                if (o == null) {
                    setDefaultArr(dataSet, v, cellStr, cnt, rnt, r, c, merge, dbObject, colAddCntMap, name, prefix, start);
                } else if (o instanceof JSONObject) {
                    JSONObject addCell = (JSONObject) o;
                    String cellValue = addCell.getString(name);
                    ReportParams reportParams = new ReportParams(dataSet, v, cellStr, cnt, rnt, r, c, merge, dbObject, colAddCntMap, cellValue, 0, 1);
                    reportParams.setRowLenMap(rowLenMap);
                    setExcelValue(reportParams);
                    //setExcel(dataSet, v, cellStr, cnt, rnt, r, c, merge, dbObject, colAddCntMap, name, 0, (JSONObject) o, 1);
                } else if (o instanceof JSONArray) {
                    JSONArray datas = (JSONArray) o;

                    if (!datas.isEmpty()) {
                        int len = datas.size();

                        logger.debug("处理list类型数据 - 数据集: {}, 字段: {}, 数据量: {}, 原始行: {}, 列: {}",
                                   dataSet.getSetCode(), name, len, r, c);

                        if ("border".equals(name)) {
                            //设置边框
                            for (int i = 0; i < len; i++) {
                                borderService.borderRangeSet(cnt, rnt, r, c, dbObject, i);
                            }
                            colAddCntMap.put(c, cnt + len - 1);

                            return;
                        }

                        if (start > 0) {
                            pName = name.substring(0, start);
                            String paramName = name.substring(start + prefix.length());
                            String[] special = paramName.split("#");
                            if (special.length > 1) {
                                model = special[0];
                                if ("DateFormat".equals(model)) {
                                    patten = special[1];
                                } else if ("Max".equals(model)) { //最大显示行数
                                    int lenSet = Integer.parseInt(special[1]);
                                    len = Math.min(lenSet, len);
                                } else if ("DefaultArr".equals(model)) {
                                    name = pName;
                                }
                            } else if (special.length == 1) {
                                model = "Horizontal";
                            }
                        }
                        //设置动态公式的扩展长度
                        dbObject.getJSONObject("calcMap").put(one, len);

                        for (int i = 0; i < len; i++) { //遍历列表数据
                            Object o1 = datas.get(i);
                            logger.debug("处理list数据第{}项 - 模式: {}, 数据: {}", i, model, o1);

                            if ("Max".equals(model)) {
                                setMaxExcel(dataSet, v, cellStr, cnt, rnt, r, c, merge, dbObject, colAddCntMap, pName, i, (JSONObject) o1, datas.size());
                                columnAndRowService.setRowLen(dbObject, r, c, cnt, i);
                            } else if ("DateFormat".equals(model)) {
                                JSONObject formatObj = (JSONObject) o1;
                                String dateStr = formatObj.getString(pName);
                                ParamContext ps = new ParamContext(dateStr, model, patten);
                                String formatResult = ps.getResult();
                                ReportParams reportParams = new ReportParams(dataSet, v, cellStr, cnt, rnt, r, c, merge, dbObject, colAddCntMap, formatResult, i, datas.size());
                                reportParams.setRowLenMap(rowLenMap);
                                setExcelValue(reportParams);
                            } else if ("Horizontal".equals(model)) {
                                //横向扩展
                                JSONObject obj = (JSONObject) o1;
                                String cellValue = obj.getString(pName);
                                setHorizontalValue(dataSet, v, cellStr, cnt, rnt, r, c, merge, dbObject, rowAddCntMap, cellValue, i, datas.size());
                                Object height = obj.get("height");
                                columnAndRowService.setHorizontalHeight(height, dbObject, r, c, cnt, i);
                            } else {
                                JSONObject field = (JSONObject) o1;
                                String cellValue = field.getString(name);
                                Object height = field.get("height");

                                logger.debug("处理list数据项 - 索引: {}, 单元格值: {}, 高度配置: {}", i, cellValue, height);

                                ReportParams reportParams = new ReportParams(dataSet, v, cellStr, cnt, rnt, r, c, merge, dbObject, colAddCntMap, cellValue, i, datas.size());
                                reportParams.setRowLenMap(rowLenMap);
                                setExcelValue(reportParams);
                                //setExcel(dataSet, v, cellStr, cnt, rnt, r, c, merge, dbObject, colAddCntMap, name, i, (JSONObject) o1, datas.size());

                                if (height instanceof Integer) { //设置行高
                                    logger.debug("使用数据中的自定义行高: {}", height);
                                    columnAndRowService.setCustomRowLen(dbObject, r, c, cnt, i, (Integer) height);
                                } else {
                                    logger.debug("使用设计时的默认行高配置");
                                    columnAndRowService.setRowLen(dbObject, r, c, cnt, i);
                                }
                            }
                        }
                    } else { //为空设置默认值
                        setDefaultArr(dataSet, v, cellStr, cnt, rnt, r, c, merge, dbObject, colAddCntMap, name, prefix, start);
                        columnAndRowService.setRowLen(dbObject, r, c, cnt, 0);
                    }
                }

            }
            // 根据起始点，自动扩展右侧和下发的表头内容
            else if (cellJObj.containsKey("title")) {
                JSONArray jsonArray = JSON.parseArray(JSON.toJSONString(cellDynamicData));
                Map<String, Integer> headRowMap = new HashMap<>();
                Map<String, Integer> headHthMap = new HashMap<>();
                // 对动态加载选取进行边框设置
                Integer levelWidth = getTreeWidth((JSONObject) JSON.toJSON(cellData));
                Integer treeMaxHeight = getTreeMaxHeight(1, (List<JSONObject>) (List) cellDynamicData);
                if (dbObject.containsKey("config")) {
                    // 边框配置列表
                    JSONArray borderInfos = dbObject.getJSONObject("config").getJSONArray("borderInfo");
                    if (Objects.isNull(borderInfos)) {
                        borderInfos = new JSONArray();
                    }
                    borderInfos.add(JSONObject.parseObject("{\n" +
                            "      \"rangeType\": \"range\",\n" +
                            "      \"borderType\": \"border-all\",\n" +
                            "      \"style\": \"1\",\n" +
                            "      \"color\": \"#000\",\n" +
                            "      \"range\": [{\n" +
                            "          \"row\": [" + (r) + ", " + (r + treeMaxHeight - 1) + "],\n" +
                            "          \"column\": [" + (c) + ", " + (c + levelWidth - 1) + "]\n" +
                            "      }]\n" +
                            "  }"));
                    dbObject.getJSONObject("config").put("borderInfo", borderInfos);
                }

                // 动态加载
                dynamicRow(cnt, rnt, r, c, dbObject, (List<JSONObject>) (List) cellDynamicData,
                        cellStr, merge, colAddCntMap,
                        rowAddCntMap, headRowMap, headHthMap, 0, (List<JSONObject>) (List) cellDynamicData);
            } else {
                // 普通渲染模式
                logger.debug("普通渲染模式 - 数据集: {}, 数据量: {}, 原始行: {}, 列: {}",
                           dataSet.getSetCode(), cellDynamicData.size(), r, c);
                //循环数据赋值
                for (int j = 0; j < cellDynamicData.size(); j++) {
                    // 扩展列类型
                    if ((Object) cellDynamicData.get(j) instanceof ArrayList) {
                        JSONArray addCell = (JSONArray) JSON.toJSON(cellDynamicData.get(j));
                        for (int i = 0; i < addCell.size(); i++) {
                            Object o = addCell.get(i);
                            //转字符串，解决深拷贝问题
                            JSONObject addCellData = JSONObject.parseObject(cellStr);
                            addCellData.put("r", cnt + r + j); //行数增加
                            addCellData.put("c", rnt + c + i);

                            addCellData.getJSONObject("v").put("ht", 0);
                            addCellData.getJSONObject("v").put("v", o);
                            addCellData.getJSONObject("v").put("m", o);
                            //处理单元格扩展之后此列扩展的总行数
                            colAddCntMap.put(c, cnt + cellDynamicData.size() - 1);
                            // 当前赋值的这一列前面已经增加了多少列  key是当前渲染实际的列  value是实际的列加上扩展的列的数量
                            rowAddCntMap.put(rnt + c + i, rnt + c + i + addCell.size() - 1);
                            ReportUtils.setCalc(addCellData, dbObject);
                            dbObject.getJSONArray("celldata").add(addCellData);
                        }

                        // 对动态加载选取进行边框设置
                        JSONArray row = JSON.parseArray(JSON.toJSONString(cellData));
                        Integer levelWidth = addCell.size();
                        Integer treeMaxHeight = cellDynamicData.size();
                        if (dbObject.containsKey("config")) {
                            // 边框配置列表
                            JSONArray borderInfos = dbObject.getJSONObject("config").getJSONArray("borderInfo");
                            if (Objects.isNull(borderInfos)) {
                                borderInfos = new JSONArray();
                            }
                            borderInfos.add(JSONObject.parseObject("{\n" +
                                    "      \"rangeType\": \"range\",\n" +
                                    "      \"borderType\": \"border-all\",\n" +
                                    "      \"style\": \"1\",\n" +
                                    "      \"color\": \"#000\",\n" +
                                    "      \"range\": [{\n" +
                                    "          \"row\": [" + (r + rnt) + ", " + (r + rnt + treeMaxHeight - 1) + "],\n" +
                                    "          \"column\": [" + (c + cnt) + ", " + (c + cnt + levelWidth - 1) + "]\n" +
                                    "      }]\n" +
                                    "  }"));
                            dbObject.getJSONObject("config").put("borderInfo", borderInfos);
                        }
                    } else if (cellDynamicData.get(j) instanceof Map) {
                        // 固定列 非动态列接口
                        //新增的行数据
                        String name = dataSet.getFieldLabel();
                        String prefix = "::";
                        String suffix = "}";
                        int start = name.indexOf(prefix);
                        String model = null;
                        String pName = null;
                        String patten = null;//时间格式
                        if (start > 0) {
                            pName = name.substring(0, start);

                            String paramName = name.substring(start + prefix.length());
                            String[] special = paramName.split("#");
                            if (special.length > 1) {
                                model = special[0];
                                if ("DateFormat".equals(model)) {
                                    patten = special[1];
                                }
                            }
                        } else {
                            pName = name;
                        }
                        JSONObject cell = (JSONObject) JSON.toJSON(cellDynamicData.get(j));
                        String cellValue = cell.getString(pName);
                        if (patten != null) {
                            ParamContext ps = new ParamContext(cellValue, model, patten);
                            cellValue = ps.getResult();
                        }

                        logger.debug("普通渲染模式处理数据项 - 索引: {}, 字段: {}, 单元格值: {}", j, pName, cellValue);

                        ReportParams reportParams = new ReportParams(dataSet, v, cellStr, cnt, rnt, r, c, merge, dbObject, colAddCntMap, cellValue, j, cellDynamicData.size());
                        setExcelValue(reportParams);
                        columnAndRowService.setRowLen(dbObject, r, c, cnt, j);
                    }
                }
            }
        }
    }

    private void mergeAppointNum(JSONObject dbObject,JSONObject cellJObj,JSONArray mergeData,
                                 String filed,String cellStr,int cnt, int rnt, int r, int c,
                                 Map<Integer, Integer> colAddCntMap,String mergeName,
                                 JSONObject merge, Integer num) {
        //切分需要合并的数据
        List<List<Object>> mergeList = Lists.partition(mergeData, num);
        JSONArray newMergeData = new JSONArray();
        AtomicInteger addRow = new AtomicInteger();
        final int[] a = {0};
         JSONObject[] addMergeCell = {null};
        mergeList.forEach(( objects) -> {
            //获取合并单元格
            addMergeCell[0] = JSONObject.parseObject(cellStr);

            JSONObject mc = addMergeCell[0].getJSONObject("v").getJSONObject("mc");
            //设置合并单元格
            if (mc != null) {
                mc.put("r", r + cnt + a[0]);
                mc.put("c", c + rnt);
                mc.put("rs", objects.size());
            }else {
                mc = new JSONObject();
                mc.put("r", r + cnt + a[0]);
                mc.put("c", c + rnt);
                mc.put("rs", objects.size());
                mc.put("cs", 1);
            }
            //进行合并单元格操作  设置占用的长度  设置合并单元格之后的值
            JSONObject jsonObject = (JSONObject) objects.get(0);
            String string = jsonObject.getString(filed);
            addMergeCell[0].put("r", cnt + r + a[0]); //行数增加
            addMergeCell[0].getJSONObject("v").put("v",string);
            addMergeCell[0].getJSONObject("v").put("m", string);
            if (num > 1) {
                addMergeCell[0].getJSONObject("v").put("mc", mc);
                //合并单元格需要处理config.merge
                merge.put(mc.getString("r") + "_" + mc.getString("c"), mc);
            }
            ReportUtils.setCalc(addMergeCell[0], dbObject);
            dbObject.getJSONArray("celldata").add(addMergeCell[0]);
            //设置边框
            borderAddSet(r, c, cnt  + a[0], rnt, num - 1, 0, dbObject, 1, 1);
            newMergeData.addAll(objects);
            addRow.addAndGet(objects.size());
            //累计占用的单元格
            a[0] += objects.size();
        });
        cellJObj.put(mergeName, newMergeData);
        Integer rMg = addMergeCell[0].getJSONObject("v").getJSONObject("mc").getInteger("cs");
        if (rMg!=null && rMg >=1 ){
            for (int i = 0;i<rMg ;i++){
                colAddCntMap.put(c + i, cnt + addRow.get() - 1);
            }

        }
    }

    /**
     * 初始化单元格合并属性的行数
     *
     * @param merge
     * @param mc
     */
    public void initCellMerge(JSONObject merge, JSONObject mc) {
        merge.put((mc.getInteger("r")) + "_" + mc.getString("c"), mc);
    }

    /**
     * 设置边框
     */
    private void borderAddSet(int r, int c, int cnt, int rnt, int rAdd, int cAdd, JSONObject dbObject, int i, int j) {
        JSONObject border = dbObject.getJSONObject("border");
        JSONObject oldBorder = dbObject.getJSONObject("oldBorder");
        //没有设置边框格式时空指针处理
        border = border != null ? border : new JSONObject();
        oldBorder = oldBorder != null ? oldBorder : new JSONObject();
        JSONArray borders = border.getJSONArray(r + "-" + c);
        JSONArray oldBorders = oldBorder.getJSONArray(r + "-" + c);
        if (borders != null && !borders.isEmpty()) {
            for (int i1 = 0; i1 < borders.size(); i1++) {
                JSONObject borderMap = borders.getJSONObject(i1);
                JSONObject old = oldBorders.getJSONObject(i1);
                JSONObject borderJson = new JSONObject();
                borderJson.put("rangeType", "range");
                String borderType = borderMap.getString("borderType");
                String color = borderMap.getString("color");
                String style = borderMap.getString("style");
                borderType = borderType != null ? borderType : "border-all";
                color = color != null ? color : "#000";
                style = style != null ? style : "1";
                borderJson.put("borderType", borderType);
                borderJson.put("color", color);
                borderJson.put("style", style);
                JSONArray range = new JSONArray();
                JSONObject range0 = new JSONObject();

                JSONArray row = borderMap.getJSONArray("row");
                JSONArray column = borderMap.getJSONArray("column");
                int len = row.getInteger(1) - row.getInteger(0);

                int[] newRow = {r + cnt, r + cnt + rAdd + len};
                int[] newColumn = {column.getInteger(0) + rnt, column.getInteger(1) + rnt + cAdd};
                //row.set(0, r +cnt);
                //row.set(1, r +cnt+ rAdd + len);
                //column.set(0, column.getInteger(0) + rnt);
                //column.set(1, column.getInteger(1) + rnt+cAdd);
                range0.put("row", newRow);
                range0.put("column", newColumn);
                range.add(range0);
                borderJson.put("range", range);

                // 边框配置列表
                JSONArray borderInfos = dbObject.getJSONObject("config").getJSONArray("borderInfo");
                borderInfos.remove(old);
                borderInfos.add(borderJson);

            }

        }
    }

    private void setDefaultArr(DataSetDto dataSet, String v, String cellStr, int cnt, int rnt, int r, int c, JSONObject merge, JSONObject dbObject, Map<Integer, Integer> colAddCntMap, String name, String prefix, int start) {
        String model;
        String[] arr;
        int len = 0;
        if (start > 0) {
            String paramName = name.substring(start + prefix.length());
            String[] special = paramName.split("#");
            if (special.length > 1) {
                model = special[0];
                if ("DefaultArr".equals(model)) {
                    String defArr = special[1];
                    arr = defArr.split("-");
                    len = arr.length;
                    for (int i = 0; i < len; i++) {
                        ReportParams reportParams = new ReportParams(dataSet, v, cellStr, cnt, rnt, r, c, merge, dbObject, colAddCntMap, arr[i], i, len);
                        setExcelValue(reportParams);
                        columnAndRowService.setRowLen(dbObject, r, c, cnt, i);
                    }
                }
            }

        }
    }


    private  String convertHtml(String content) {

        // <p>段落替换为换行

        content = content.replaceAll("<p.*?>", "\r\n");

        // <br><br/>替换为换行

        content = content.replaceAll("<br\\s*/?>", "\r\n");

        // 去掉其它的<>之间的信息

        content = content.replaceAll("\\<.*?>", "");

        return content.replaceAll("(\r?\n(\\s*\r?\n)+)","\r\n");

    }


    private void setExcelValue(ReportParams params) {
        DataSetDto dataSet = params.getDataSet();
        String v = params.getV();
        String cellStr = params.getCellStr();
        int cnt = params.getCnt();
        int rnt = params.getRnt();
        int r = params.getR();
        int c = params.getC();
        JSONObject merge = params.getMerge();
        JSONObject dbObject = params.getDbObject();
        Map<Integer, Integer> colAddCntMap = params.getColAddCntMap();
        String cellValue = params.getCellValue();
        int i = params.getI();
        int size = params.getSize();
        //JSONObject addCell = params.getAddCell();
        Map<Integer, List<ReportLenVO>> rowLenMap = params.getRowLenMap() != null? params.getRowLenMap() : new HashMap<>();

        int cellRow = cnt + r + i;
        int cellCol = c + rnt;

        borderService.borderRangeSet(cnt, rnt, r, c, dbObject, i);

        //字段
        String replace = "";
        // 当前行是否需要计算新增行
        Boolean hasAddRow = v.contains("${");

        if (StringUtils.isNotBlank(cellValue)) {
            replace = v.replace((hasAddRow ? "${" : "#{").concat(dataSet.getSetCode()).concat(".").concat(dataSet.getFieldLabel()).concat("}"), cellValue);
            if (v.contains("rich_text_format_")){
                replace = convertHtml(replace);
            }
        }
        //转字符串，解决深拷贝问题
        JSONObject addCellData = JSONObject.parseObject(cellStr);

        JSONObject vObj = addCellData.getJSONObject("v");

        JSONObject cellMc = vObj.getJSONObject("mc");

        String tb = vObj.getString("tb");
        if (tb != null && tb.equals("2")){ //2为自动换行,自动换行情况下动态设置扩展行的高度
            String fontName = vObj.getString("ff") != null ? vObj.getString("ff") : "宋体";
            Integer fontSize = vObj.getInteger("fs") != null ? vObj.getInteger("fs") : 10;

            //宽度 单元格内容 字体
            JSONObject columnLen = dbObject.getJSONObject("config").getJSONObject("columnlen");
            JSONObject rowlenObj = dbObject.getJSONObject("config").getJSONObject("rowlen");
            int defColLen = 73;
            Integer colLen = columnLen.getInteger(String.valueOf(c)) != null ? columnLen.getInteger(String.valueOf(c)) : defColLen; //luckysheet单元格默认宽度
            Integer rowLen = rowlenObj.getInteger(String.valueOf(r)) != null ? rowlenObj.getInteger(String.valueOf(r)) : 19;

            if (null != cellMc) {
                Integer cs = cellMc.getInteger("cs"); //合并单元格宽度
                for (int col = 1; col < cs; col++) { //从1开始 第一列的宽度为初始的colLen
                    colLen += columnLen.getInteger(String.valueOf(c + col)) != null ? columnLen.getInteger(String.valueOf(c + col)) : defColLen;
                }
            }

            Font font = new Font(fontName, Font.PLAIN, fontSize);

            ReportLenVO reportLenVO = new ReportLenVO();
            reportLenVO.setRow(cellRow);
            reportLenVO.setCol(cellCol);
            reportLenVO.setFont(font);
            reportLenVO.setColLen(colLen);
            reportLenVO.setRowLen(rowLen);
            reportLenVO.setCellValue(replace);

            if (rowLenMap.containsKey(cellRow)){
                rowLenMap.get(cellRow).add(reportLenVO);
            }else {
                List<ReportLenVO> reportLenList = new ArrayList<>();
                reportLenList.add(reportLenVO);
                rowLenMap.put(cellRow, reportLenList);
            }
        }

        boolean isText = false;
        Double dVal = 0d;
        boolean isD = false;

        if (vObj.containsKey("ct")) {
            JSONObject ctObj = vObj.getJSONObject("ct");
            if (ctObj.containsKey("fa") &&
                    ctObj.containsKey("t") &&
                    "@".equals(ctObj.getString("fa")) &&
                    "s".equals(ctObj.getString("t")))
            {
                isText = true;
            }
        }

        try{
            dVal = Double.parseDouble(replace);
            isD = true;
        }catch (Exception e){

        }

        addCellData.put("r", cellRow); //行数增加
        addCellData.put("c", cellCol);
        if (replace.contains("\r\n")) {
            JSONObject oldV = addCellData.getJSONObject("v");
            oldV.remove("m");
            oldV.remove("v");
            String ff = oldV.getString("ff");
            String fs = oldV.getString("fs");
            Integer bl = oldV.getInteger("bl");
            String ht = oldV.getString("ht");
            String vt = oldV.getString("vt");

            JSONObject ct = new JSONObject();
            ct.put("fa", "General");
            ct.put("t", "inlineStr");
            JSONArray s = new JSONArray();
            JSONObject s_v = new JSONObject();
            s_v.put("v", replace);
            s_v.put("m",replace);

            s_v.put("ff", ff);
            s_v.put("fs", fs);
            s_v.put("bl", bl);
            s_v.put("ht", ht);
            s_v.put("vt", vt);
            s.add(s_v);
            ct.put("s", s);
            oldV.put("ct", ct);

        } else {

            if (isD && !isText) {
                JSONObject ct = new JSONObject();
                ct.put("t", "n");
                ct.put("fa", "General");
                addCellData.getJSONObject("v").put("ct", ct);
                addCellData.getJSONObject("v").put("v", dVal);
                addCellData.getJSONObject("v").put("m", String.valueOf(dVal));
            }else {
                addCellData.getJSONObject("v").put("v", replace);
                addCellData.getJSONObject("v").put("m", replace);
            }
        }

        //判断是否是合并单元格
        if (null != cellMc) {
            //处理合并单元格
            Integer rs = cellMc.getInteger("rs");
            Integer cs = cellMc.getInteger("cs");
            cellMc.put("r", cnt + r + rs * i); //行数增加
            cellMc.put("c", c);
            addCellData.put("r", cnt + r + rs * i);
            //合并单元格需要处理config.merge
            merge.put(cellMc.getString("r") + "_" + cellMc.getString("c"), cellMc);
            //处理单元格扩展之后此列扩展的总行数
            int addRowNum = hasAddRow ? cnt : cnt + rs * size - 1;
            colAddCntMap.put(c, addRowNum);
            //合并的单元格也增加扩展行数记录
            for (int rsi = 1; rsi < cs; rsi++) {
                colAddCntMap.put(c + rsi, addRowNum);
            }
        } else {
            //处理单元格扩展之后此列扩展的总行数
            colAddCntMap.put(c, hasAddRow ? cnt : cnt + size - 1);
        }
        ReportUtils.setCalc(addCellData, dbObject);
        dbObject.getJSONArray("celldata").add(addCellData);
    }

    private void setHorizontalValue(DataSetDto dataSet, String v, String cellStr, int cnt, int rnt, int r, int c, JSONObject merge, JSONObject dbObject, Map<Integer, Integer> rowAddCntMap, String cellValue, int i, int size) {

        borderService.borderRangeSet(cnt, rnt, r, c, dbObject, i);

        //字段
        String replace = "";
        // 当前行是否需要计算新增行
        Boolean hasAddRow = v.contains("${");

        if (StringUtils.isNotBlank(cellValue)) {
            replace = v.replace((hasAddRow ? "${" : "#{").concat(dataSet.getSetCode()).concat(".").concat(dataSet.getFieldLabel()).concat("}"), cellValue);
        }
        //转字符串，解决深拷贝问题
        JSONObject addCellData = JSONObject.parseObject(cellStr);

        addCellData.put("r", r);
        addCellData.put("c", c + rnt + i);//列数增加

        addCellData.getJSONObject("v").put("v", replace);
        addCellData.getJSONObject("v").put("m", replace);

        JSONObject cellMc = addCellData.getJSONObject("v").getJSONObject("mc");
        //判断是否是合并单元格
        if (null != cellMc) {
            //处理合并单元格
            Integer rs = cellMc.getInteger("rs");
            cellMc.put("r", r); //行数增加
            cellMc.put("c", c + rnt + rs * i);
            addCellData.put("c", rnt + c + rs * i);
            //合并单元格需要处理config.merge
            merge.put(cellMc.getString("c") + "_" + cellMc.getString("r"), cellMc);
            //处理单元格扩展之后此列扩展的总行数
            rowAddCntMap.put(r, hasAddRow ? rnt : rnt + rs * size - 1);
        } else {
            //处理单元格扩展之后此列扩展的总行数
            rowAddCntMap.put(r, hasAddRow ? rnt : rnt + size - 1);
        }
        ReportUtils.setCalc(addCellData, dbObject);
        dbObject.getJSONArray("celldata").add(addCellData);
    }


    private void setMaxExcel(DataSetDto dataSet, String v, String cellStr, int cnt, int rnt, int r, int c, JSONObject merge, JSONObject dbObject, Map<Integer, Integer> colAddCntMap, String name, int i, JSONObject addCell, int size) {

        //borderSet(cnt, rnt, r, c, dbObject, i);

        //字段
        String fieldLabel = addCell.getString(name);
        String replace = "";
        // 当前行是否需要计算新增行
        Boolean hasAddRow = v.contains("${");

        if (StringUtils.isNotBlank(fieldLabel)) {
            replace = v.replace((hasAddRow ? "${" : "#{").concat(dataSet.getSetCode()).concat(".").concat(dataSet.getFieldLabel()).concat("}"), fieldLabel);
        }
        //转字符串，解决深拷贝问题
        JSONObject addCellData = JSONObject.parseObject(cellStr);

        addCellData.put("r", r + i); //行数增加
        addCellData.put("c", c + rnt);
        addCellData.getJSONObject("v").put("v", replace);
        addCellData.getJSONObject("v").put("m", replace);
        JSONObject cellMc = addCellData.getJSONObject("v").getJSONObject("mc");
        //判断是否是合并单元格
        if (null != cellMc) {
            //处理合并单元格
            Integer rs = cellMc.getInteger("rs");
            cellMc.put("r", cnt + r + rs * i); //行数增加
            cellMc.put("c", c);
            addCellData.put("r", cnt + r + rs * i);
            //合并单元格需要处理config.merge
            merge.put(cellMc.getString("r") + "_" + cellMc.getString("c"), cellMc);
            //处理单元格扩展之后此列扩展的总行数
            //colAddCntMap.put(c, hasAddRow ? cnt : cnt + rs * size - 1);
        } else {
            //处理单元格扩展之后此列扩展的总行数
            //colAddCntMap.put(c, hasAddRow ? cnt : cnt + size - 1);
        }
        ReportUtils.setCalc(addCellData, dbObject);
        dbObject.getJSONArray("celldata").add(addCellData);
    }

    /**
     * 获取当前树最宽宽度
     */
    public static Integer getTreeWidth(JSONObject tree) {
        if (tree == null || !tree.containsKey("child") || tree.getJSONArray("child").size() == 0) {
            return 1;
        }
        List<Integer> wit = new ArrayList<>();
        JSONArray child = tree.getJSONArray("child");
        for (int i = 0; i < child.size(); i++) {
            wit.add(getTreeWidth((JSONObject) child.get(i)));
        }
        Integer reduce = wit.stream().reduce(0, (x, y) -> x + y);

        return reduce;
    }

    /**
     * 获取当前树最宽宽度
     */
    public static Integer getTreeMaxHeight(Integer level, List<JSONObject> cellDynamicData) {
        if (cellDynamicData == null || cellDynamicData.size() == 0) {
            return level;
        }
        List<Integer> wit = new ArrayList<>();
        for (int i = 0; i < cellDynamicData.size(); i++) {
            JSONObject cellDynamicDatum = JSON.parseObject(JSON.toJSONString(cellDynamicData.get(i)));
            if (cellDynamicDatum.containsKey("child")) {
                JSONArray child = cellDynamicDatum.getJSONArray("child");
                wit.add(getTreeMaxHeight(1, child.toJavaList(JSONObject.class)));
            } else {
                wit.add(0);
            }
        }
        Integer reduce = wit.stream().max((x, y) -> x - y).get();
        return level + reduce;
    }


    public void dynamicRow(int cnt, int rnt, int r, int c, JSONObject dbObject, List<JSONObject> cellDynamicData,
                           String cellStr, JSONObject merge,
                           Map<Integer, Integer> colAddCntMap,
                           Map<Integer, Integer> rowAddCntMap,
                           Map<String, Integer> headRowMap,
                           Map<String, Integer> headHthMap, Integer level, List<JSONObject> allDynamicData) {
        if (cellDynamicData == null) return;
        int thisRowAdd = 0;
        for (int j = 0; j < cellDynamicData.size(); j++) {
            //当前真实渲染的列数
            int copyC = c + j + thisRowAdd;
            JSONObject addCell = (JSONObject) JSON.toJSON(cellDynamicData.get(j));
            //转字符串，解决深拷贝问题
            JSONObject addCellData = JSONObject.parseObject(cellStr);
            int rc = headRowMap.getOrDefault(copyC + "_" + r, 0);
            int rnc = headHthMap.getOrDefault(copyC + "_" + r, 0);
            // 处理当前行应该占的宽度
            Integer treeWidth = getTreeWidth(addCell);
            // 当前行的列占用多列，则把c累积
            if (treeWidth > 1) {
                thisRowAdd = thisRowAdd + treeWidth - 1;
            }
            addCellData.put("r", r + rnc); //行数增加
            addCellData.put("c", copyC + rc);// 列数增加
            addCellData.getJSONObject("v").put("ht", 0);
            addCellData.getJSONObject("v").put("v", addCell.get("title"));
            addCellData.getJSONObject("v").put("m", addCell.get("title"));


            // 基础的扩展格
            // 需要合并的行数量
            Integer treeHeight = 1;
            // 没有子集需要扩展的高度就是同级深的数据
            if (!addCell.containsKey("child")) {
                treeHeight = getLevelHeight(level, allDynamicData);
            }
            if (treeWidth > 1 || treeHeight > 1) {
                JSONObject cellMc = new JSONObject();
                addCellData.put("mc", cellMc);
                cellMc.put("r", r + rnc); //行数增加
                cellMc.put("c", copyC + rc);
                cellMc.put("rs", treeHeight); //需合并的行数量
                cellMc.put("cs", treeWidth); //需要合并的列数量
                merge.put(cellMc.getString("r") + "_" + cellMc.getString("c"), cellMc);
                addCellData.getJSONObject("v").put("mc", cellMc);
            }
            dbObject.getJSONArray("celldata").add(addCellData);
            // 处理子级单元格
            if (addCell.containsKey("child")) {
//                 子集的行从当前行开始值加上所占高度  列从当前列加上合并的列长度
                this.dynamicRow(cnt, rnt, r + treeHeight, copyC + rc, dbObject, (List<JSONObject>) addCell.get("child"), cellStr,
                        merge, colAddCntMap, rowAddCntMap, headRowMap, headHthMap, level + 1, allDynamicData);
            }
            Integer treeMaxHeight = getTreeMaxHeight(1, cellDynamicData);
            for (int i = 0; i < treeMaxHeight; i++) {
                // 新增行只取最大增加的那一次行數
                if (!colAddCntMap.containsKey(r + rnc + i) || colAddCntMap.get(r + rnc + i) < (cnt + treeMaxHeight - 1)) {
                    colAddCntMap.put(r + rnc + i, cnt + treeMaxHeight - 1);
                }
            }
            // 保存行需要新增数量在子集单元格之后保存
            for (int i = 0; i < treeHeight; i++) {
                for (int cs = 0; cs < treeWidth; cs++) {
                    headRowMap.put((copyC + rc + cs) + "_" + (r + rnc + i), treeWidth - 1);
                }
            }
        }

    }

    /**
     *
     */
    public static Integer getLevelHeight(Integer level, List<JSONObject> cellDynamicData) {
        if (level == 0) {
            JSONArray jsonArray = JSON.parseArray(JSON.toJSONString(cellDynamicData));
            return jsonArray.stream().map(json -> getTreeHeight(JSON.parseObject(JSON.toJSONString(json)), 1)).max((x, y) -> x - y).get();
        }
        return getLevelHeight(level - 1, getChildObject(cellDynamicData));
    }

    /**
     * 获取当前树最高层级
     */
    public static Integer getTreeHeight(JSONObject tree, Integer max) {
        if (tree == null || !tree.containsKey("child") || tree.getJSONArray("child").size() == 0) {
            return max;
        }
        List<Integer> heights = new ArrayList<>();
        JSONArray child = tree.getJSONArray("child");
        for (int i = 0; i < child.size(); i++) {
            heights.add(getTreeHeight((JSONObject) child.get(i), 1));
        }
        return heights.stream().max((x, y) -> x - y).get() + max;
    }

    public static List<JSONObject> getChildObject(List<JSONObject> cellDynamicData) {
        List<JSONObject> jsonObjects = new ArrayList<>();
        JSONArray jsonArray = JSON.parseArray(JSON.toJSONString(cellDynamicData));
        jsonArray.stream().forEach(e -> {
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(e));
            if (jsonObject.containsKey("child")) {
                jsonObjects.addAll(jsonObject.getJSONArray("child").toJavaList(JSONObject.class));
            }
        });
        return jsonObjects;
    }

}
