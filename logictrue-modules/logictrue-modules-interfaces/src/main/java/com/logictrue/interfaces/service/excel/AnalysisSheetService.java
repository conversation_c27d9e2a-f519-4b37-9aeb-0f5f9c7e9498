package com.logictrue.interfaces.service.excel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.logictrue.interfaces.domain.dto.DataSetDto;
import com.logictrue.interfaces.domain.vo.ReportLenVO;
import com.logictrue.interfaces.domain.vo.PreviewVO;
import com.logictrue.interfaces.strategy.param.PicParam;
import com.logictrue.interfaces.utils.CellType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service(value = "analysisSheetService")
public class AnalysisSheetService {

    private static final Logger logger = LoggerFactory.getLogger(AnalysisSheetService.class);

    @Autowired
    private ShareService shareService;
    @Autowired
    private DynamicCellService dynamicCellService;
    @Autowired
    private ColumnAndRowService columnAndRowService;
    @Autowired
    private StaticCellService staticCellService;
    @Autowired
    private BorderService borderService;
    @Autowired
    private ChartService chartService;
    @Autowired
    private RowAndCellDynamicService rowAndCellDynamicService;
    @Autowired
    private CellRenderService cellRenderService;
    @Autowired
    private CalculateService calculateService;
    @Autowired
    private Cell2DArrayService cell2DArrayService;

    /**
     * 解析单sheet celldata
     *
     * @param dbObject
     */
    public void analysisSheetCellData(JSONObject dbObject, String setParam, PreviewVO previewVO) {
        //清空data值
        dbObject.remove("data");
        //celldata是一个一维数组
        if (dbObject.containsKey("celldata") && null != dbObject.get("celldata")) {
            List<JSONObject> celldata = new ArrayList<>();
            celldata.addAll((List<JSONObject>) dbObject.get("celldata"));

            //整理celldata数据，转换为map格式，方便后续使用单元格位置获取对应的cell对象
            Map<String, JSONObject> cellDataMap = cellDataList2Map(celldata);
            //清除原有的数据
            dbObject.getJSONArray("celldata").clear();
            //获取配置项中的合并属性
            JSONObject merge = dbObject.getJSONObject("config").getJSONObject("merge");
            if (merge != null) {
                merge.clear();
            } else {
                merge = new JSONObject();
                dbObject.getJSONObject("config").put("merge", merge);
            }
            //定义存储每一列动态扩展的行数
            Map<Integer, Integer> colAddCntMap = new HashMap<>();
            //定义存储每一列动态扩展的列数
            Map<Integer, Integer> rowAddCntMap = new HashMap<>();

            //边框信息初始化 保存初始边框信息
            JSONArray borders = dbObject.getJSONObject("config").getJSONArray("borderInfo");
            if (borders != null) {
                //边框配置 多个单元格区间边框 和 单个单元格边框 数据格式不一致
                Map<String, List<JSONObject>> borderMap = new HashMap<>();

                Map<String, List<JSONObject>> oldBorder = new HashMap<>();

                for (int i = 0; i < borders.size(); i++) {
                    JSONObject border = borders.getJSONObject(i);
                    List<JSONObject> _border = new ArrayList<>();
                    List<JSONObject> _oldBorder = new ArrayList<>();
                    JSONObject jsonObject = new JSONObject();
                    Integer rowStart = null;
                    Integer columnStart = null;
                    JSONArray ranges = border.getJSONArray("range");
                    String color = border.getString("color");
                    String style = border.getString("style");
                    String borderType = border.getString("borderType");
                    jsonObject.put("color", color);
                    jsonObject.put("style", style);
                    jsonObject.put("borderType", borderType);
                    JSONObject borderCell = border.getJSONObject("value");
                    if (ranges != null && !ranges.isEmpty()) {
                        JSONObject range = ranges.getJSONObject(0);

                        jsonObject.put("range", range);
                        JSONArray row = range.getJSONArray("row");
                        JSONArray column = range.getJSONArray("column");
                        rowStart = row.getInteger(0);
                        columnStart = column.getInteger(0);
                        jsonObject.put("row", row);
                        jsonObject.put("column", column);

                    } else if (borderCell != null) {
                        rowStart = borderCell.getInteger("row_index");
                        columnStart = borderCell.getInteger("col_index");
                        int[] row = {rowStart, rowStart};
                        int[] column = {columnStart, columnStart};
                        jsonObject.put("row", row);
                        jsonObject.put("column", column);
                    }
                    if (rowStart != null && columnStart != null) {
                        String key = rowStart + "-" + columnStart;
                        if (borderMap.containsKey(key)) {
                            borderMap.get(key).add(jsonObject);
                            oldBorder.get(key).add(border);
                        } else {
                            _border.add(jsonObject);
                            _oldBorder.add(border);
                            borderMap.put(rowStart + "-" + columnStart, _border);
                            oldBorder.put(rowStart + "-" + columnStart, _oldBorder);
                        }
                    }
                }
                dbObject.put("border", borderMap);
                dbObject.put("oldBorder", oldBorder);

            }

            //行高信息初始化 保存初始行高信息
            JSONObject rowlen = dbObject.getJSONObject("config").getJSONObject("rowlen");
            if (rowlen != null) {
                dbObject.put("rowlen", JSON.parseObject(rowlen.toString()));
                //rowlen.clear();
            }

            JSONObject customHeight = dbObject.getJSONObject("config").getJSONObject("customHeight");
            if (customHeight == null) {
                customHeight = new JSONObject();
                dbObject.getJSONObject("config").put("customHeight", customHeight);
            }

            dbObject.put("colAddCntMap", colAddCntMap); //列(col)增加的行数记录

            JSONObject calc = new JSONObject();

            dbObject.put("calcMap", calc);

            JSONArray formulaCells = new JSONArray();

            dbObject.put("formulaCells", formulaCells);

            //记录每一行的最高行高
            Map<Integer, List<ReportLenVO>> rowLenMap = new HashMap<>();
            // 遍历已存在的单元格，查看是否存在动态参数
            for (int i = 0; i < celldata.size(); i++) {
                //单元格对象
                JSONObject cellObj = celldata.get(i);
                //fastjson深拷贝问题
                String cellStr = cellObj.toJSONString();
                analysisCellData(cellObj, previewVO, setParam, colAddCntMap, rowAddCntMap, cellStr, merge, dbObject, cellDataMap, rowLenMap);
            }
            //解析设置了自动换行的动态行高
            analysisRowLenMap(rowLenMap, dbObject);

            //处理所有图片的位置计算（在自动行高计算完成后）
            PicParam.processAllImagePositions(dbObject);

            calculateService.calculateFormulaCells(dbObject.getJSONArray("formulaCells"));

            dbObject.remove("luckysheet_selection_range"); //清空选择区域

            //移除用于动态解析的缓存信息
            dbObject.remove("border");
            dbObject.remove("oldBorder");
            dbObject.remove("rowlen");
            dbObject.remove("colAddCntMap");
            dbObject.remove("calcMap");
            dbObject.remove("formulaCells");
        }
    }

    /**
     * list转为map结构，方便使用行列号查找对应cell对象
     *
     * @param cellDataList
     * @return
     */
    public Map<String, JSONObject> cellDataList2Map(List<JSONObject> cellDataList) {
        Map<String, JSONObject> cellDataMap = new HashMap<>();
        for (JSONObject cellData : cellDataList) {
            String r = cellData.getString("r");
            String c = cellData.getString("c");
            cellDataMap.put(r + "," + c, cellData);
        }
        return cellDataMap;
    }

    /**
     * 开始解析并渲染 cellData
     *
     * @param cellObject
     */
    public void analysisCellData(JSONObject cellObject, PreviewVO previewVO, String setParam,
                                 Map<Integer, Integer> colAddCntMap, Map<Integer, Integer> rowAddCntMap,
                                 String cellStr, JSONObject merge, JSONObject dbObject, Map<String, JSONObject> cellDataMap,
                                 Map<Integer, List<ReportLenVO>> rowLenMap) {
        //获取行号
        Integer cellR = cellObject.getInteger("r");
        //获取列数
        Integer cellC = cellObject.getInteger("c");
        //获取此行已经动态增加的行数，默认0行
        int cnt = colAddCntMap.get(cellC) == null ? 0 : colAddCntMap.get(cellC);

        int rnt = rowAddCntMap.get(cellR) == null ? 0 : rowAddCntMap.get(cellR);

        //扩展后的行,动态扩展的列数
        int rowDynamicColumnNum = rowAddCntMap.get(cellR + cnt) == null ? 0 : rowAddCntMap.get(cellR + cnt);

        //获取单元格类型
        CellType cellType = shareService.getCellType(cellObject);
        switch (cellType) {
            case BLACK:
                //空数据单元格不处理
                columnAndRowService.setRowLen(dbObject, cellR, cellC, cnt, 0);
                break;
            case DYNAMIC_MERGE:
            case DYNAMIC:
                //处理动态单元格
                String v = cellObject.getJSONObject("v").getString("v");
                DataSetDto dataSet = shareService.getDataSet(v, setParam);
                boolean isContinue = specialType(previewVO, colAddCntMap, rowAddCntMap, dbObject, cellR, cellC, cnt, rowDynamicColumnNum, v, cellStr, merge);
                if (isContinue) {
                    dynamicCellService.handleDynamicCellObject(dataSet, previewVO, v, cellStr, cnt, rowDynamicColumnNum, cellR,
                            cellC, merge, dbObject, colAddCntMap, rowAddCntMap, rowLenMap);
                }
                break;
            case CALC:
                //公式计算
                calculateService.handleCalculateCell(cellStr, dbObject, merge);
                break;
            default:
                //处理静态单元格
                staticCellService.handleStaticCellObject(cellStr, dbObject, cnt, rowDynamicColumnNum, cellR, cellC, cellDataMap, setParam, merge, colAddCntMap, rowAddCntMap, cellType);

                borderService.borderRangeSet(cnt, rowDynamicColumnNum, cellR, cellC, dbObject, 0);
                columnAndRowService.setStaticRowLen(dbObject, cellR, cellC, cnt);
                break;
        }
    }

    private boolean specialType(PreviewVO previewVO, Map<Integer, Integer> colAddCntMap, Map<Integer, Integer> rowAddCntMap, JSONObject dbObject, Integer cellR, Integer cellC, int cnt, int rnt, String v, String cellStr, JSONObject merge) {
        if (v.contains("###jfreeLineChart")) {

            chartService.createJfreeLineChart(dbObject, v, previewVO, cnt);

        } else if (v.contains("###lineChart")) {

            chartService.createLineChart(dbObject, v, previewVO, cnt);

        } else if (v.contains("###RowAndCell")) {
            rowAndCellDynamicService.twoWayExtend(dbObject, v, previewVO, cnt, rnt, cellR, cellC, colAddCntMap, rowAddCntMap, cellStr, merge);
            return false;
        } else if (v.contains("###CellRender")) {
            cellRenderService.render(dbObject, v, previewVO, cnt, rnt, cellR, cellC, colAddCntMap, rowAddCntMap, cellStr, merge);
            return false;
        } else if (v.contains("###Cell2DArray")) {
            cell2DArrayService.render(dbObject, v, previewVO, cnt, rnt, cellR, cellC, colAddCntMap, rowAddCntMap, cellStr, merge);
            return false;
        }
        return true;
    }

    private void analysisRowLenMap(Map<Integer, List<ReportLenVO>> rowLenMap, JSONObject dbObject) {

        JSONObject config = dbObject.getJSONObject("config");
        JSONObject rowlen = config.getJSONObject("rowlen");

        if (rowlen == null) {
            rowlen = new JSONObject();
            config.put("rowlen", rowlen);
        }

        JSONObject finalRowlen = rowlen;
        rowLenMap.forEach((row, reportLenVOS) -> {
            int tempLen = 0;//当前行最大高度
            int oldRowLen = 0;
            for (ReportLenVO reportLenVO : reportLenVOS) {
                int height = calculateTextHeight(reportLenVO.getCellValue(), reportLenVO.getColLen(), reportLenVO.getFont());
                logger.debug("自动换行计算 - 第{}行，单元格内容长度: {}, 计算高度: {}", row, reportLenVO.getCellValue().length(), height);
                if (height > tempLen) {
                    tempLen = height + 2;
                }
                if (oldRowLen < reportLenVO.getRowLen()) {
                    oldRowLen = reportLenVO.getRowLen();
                }
            }

            int finalHeight = Math.max(oldRowLen, tempLen);
            logger.debug("行高设置 - 第{}行: 原始高度={}, 自动计算高度={}, 最终高度={}",
                    row, oldRowLen, tempLen, finalHeight);

            finalRowlen.put(String.valueOf(row), finalHeight);
        });
    }

    public int calculateTextHeight(String text, int width, Font font) {
        // 创建一个虚拟的 BufferedImage 来获取 Graphics2D 对象
        BufferedImage img = new BufferedImage(1, 1, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = img.createGraphics();

        // 设置字体和抗锯齿
        g2d.setFont(font);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

        // 获取字体度量
        FontMetrics fm = g2d.getFontMetrics();

        // 初始化行高和总高度
        int lineHeight = fm.getHeight() - 1;
        double other = (double) lineHeight / 2; //luckysheet高度计算的相关参数
        double totalHeight = 0;

        // 初始化当前行的宽度和行数
        int currentLineWidth = 0;
        int lineCount = 0;

        int spaceWidth = 2; //间隙
        double coefficient = 1.33299;
        for (int i = 0; i < text.length(); i++) {
            char character = text.charAt(i);
            int charWidth = (int) Math.ceil((fm.charWidth(character) + spaceWidth) * coefficient);

            // 如果当前行加上新字符的宽度超过给定宽度，则换行
            if (currentLineWidth + charWidth > width) {
                lineCount++;
                totalHeight += (lineHeight + other);
                currentLineWidth = charWidth; // 新字符开始新的一行
            } else {
                currentLineWidth += charWidth;
            }
        }

        // 加上最后一行的行高
        totalHeight += lineHeight;

        // 释放资源
        g2d.dispose();

        return (int) totalHeight;
    }


}
