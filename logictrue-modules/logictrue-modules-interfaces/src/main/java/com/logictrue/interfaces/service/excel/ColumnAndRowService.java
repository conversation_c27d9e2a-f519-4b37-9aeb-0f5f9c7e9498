package com.logictrue.interfaces.service.excel;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service(value = "columnAndRowService")
public class ColumnAndRowService {

    private static final Logger logger = LoggerFactory.getLogger(ColumnAndRowService.class);

    /**
     * 设置行高
     */
    public void setRowLen(JSONObject dbObject, int r, int c, int cnt, int i) {
        if (c > 1) {
            return;
        }

        logger.debug("设置list数据行高 - 原始行: {}, 列: {}, 动态行偏移: {}, 数据索引: {}", r, c, cnt, i);

        JSONObject rowlen = dbObject.getJSONObject("rowlen");
        JSONObject newRowlen = dbObject.getJSONObject("config").getJSONObject("rowlen");
        JSONObject customHeight = dbObject.getJSONObject("config").getJSONObject("customHeight");

        /**
         * 动态单元格设置行高
         * cnt为已经动态新增的行数
         * 当 行数r < cnt 时 新的动态行数i=0也需要设置行高
         */
        if (rowlen != null) {
            Integer rLen = rowlen.getInteger(r + "");
            logger.debug("设计时配置的行高 - 第{}行: {}", r, rLen);

            if (rLen != null && customHeight != null) {
                int actualRow = r + cnt + i;
                newRowlen.put(actualRow + "", new Integer(rLen));
                customHeight.put(actualRow + "", 1);
                logger.debug("实际渲染行高设置 - 第{}行: {} (原始行{} + 动态偏移{} + 数据索引{})",
                           actualRow, rLen, r, cnt, i);
            }
            Integer newO = newRowlen.getInteger(r + "");
            if (rLen != null && rLen == newO) {
                newRowlen.remove(r + "", newO);
                logger.debug("移除重复的原始行高配置 - 第{}行", r);
            }
        } else {
            logger.debug("未找到设计时行高配置，使用默认行高");
        }

    }

    /**
     * 动态单元格之后的静态单元格高度设置
     */
    public void setStaticRowLen(JSONObject dbObject, int r, int c, int cnt) {
        setRowLen(dbObject, r, c, cnt - 1, 1);
        JSONObject rowlen = dbObject.getJSONObject("rowlen");
        if (rowlen != null) {
            JSONObject newRowlen = dbObject.getJSONObject("config").getJSONObject("rowlen");
            Integer o = rowlen.getInteger(r + "");
            Integer newO = newRowlen.getInteger(r + "");
            if (o != null && o == newO) {
                newRowlen.remove(r + "", newO);
            }
        }
    }

    /**
     * 设置指定行高
     */
    public void setCustomRowLen(JSONObject dbObject, int r, int c, int cnt, int i, int height) {
        if (c > 1) {
            return;
        }

        logger.debug("设置list数据自定义行高 - 原始行: {}, 列: {}, 动态行偏移: {}, 数据索引: {}, 自定义高度: {}",
                    r, c, cnt, i, height);

        JSONObject rowlen = dbObject.getJSONObject("rowlen");
        JSONObject newRowlen = dbObject.getJSONObject("config").getJSONObject("rowlen");
        JSONObject customHeight = dbObject.getJSONObject("config").getJSONObject("customHeight");

        /**
         * 动态单元格设置行高
         * cnt为已经动态新增的行数
         * 当 行数r < cnt 时 新的动态行数i=0也需要设置行高
         */
        if (rowlen != null) {
            Integer rLen = rowlen.getInteger(r + "");
            logger.debug("设计时配置的原始行高 - 第{}行: {}", r, rLen);

            if (rLen != null && customHeight != null) {
                int actualRow = r + cnt + i;
                newRowlen.put(actualRow + "", height);
                customHeight.put(actualRow + "", 1);
                logger.debug("实际渲染自定义行高设置 - 第{}行: {} (覆盖原始高度{}, 原始行{} + 动态偏移{} + 数据索引{})",
                           actualRow, height, rLen, r, cnt, i);
            }
            Integer newO = newRowlen.getInteger(r + "");
            if (rLen != null && rLen == newO) {
                newRowlen.remove(r + "", newO);
                logger.debug("移除重复的原始行高配置 - 第{}行", r);
            }
        } else {
            logger.debug("未找到设计时行高配置，直接设置自定义行高: {}", height);
        }
    }

    public void setListHeight(Object height, JSONObject dbObject, int r, int c, int cnt, int i) {
        logger.debug("设置list数据行高 - 原始行: {}, 列: {}, 动态行偏移: {}, 数据索引: {}, 高度对象: {}",
                    r, c, cnt, i, height);

        if (height instanceof Integer) { //设置行高
            logger.debug("使用数据中指定的自定义行高: {}", height);
            setCustomRowLen(dbObject, r, c, cnt, i, (Integer) height);
        } else {
            logger.debug("使用设计时配置的默认行高");
            setRowLen(dbObject, r, c, cnt, i);
        }
    }

    public void setHorizontalHeight(Object height, JSONObject dbObject, int r, int c, int cnt, int i) {
        logger.debug("设置横向扩展数据行高 - 原始行: {}, 列: {}, 动态行偏移: {}, 数据索引: {}, 高度对象: {}",
                    r, c, cnt, i, height);

        if (height instanceof Integer) { //设置行高
            logger.debug("使用横向扩展数据中指定的自定义行高: {}", height);
            setCustomRowLen(dbObject, r, c, cnt, i, (Integer) height);
        } else {
            logger.debug("横向扩展数据未指定行高，保持默认");
        }
    }

    public void setDynamicLenList(){

    }


}
