package com.logictrue.interfaces.strategy.param;


import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

public class PicParam extends ParamStrategy {

    private static final Logger logger = LoggerFactory.getLogger(PicParam.class);

    private JSONObject dbObject;

    public PicParam(JSONObject dbObject) {
        this.dbObject = dbObject;
    }

    @Override
    public String format(String param, String pattern) {

        String[] pics = pattern.split("#");
        String[] position = pics[1].split("-");
        int row1 = Integer.parseInt(position[0]);
        int column1 = Integer.parseInt(position[1]);
        int row2 = Integer.parseInt(position[2]);
        int column2 = Integer.parseInt(position[3]);

        // Debug: 打印设计时配置的位置信息
        logger.debug("图片设计时配置位置 - 起始行: {}, 起始列: {}, 结束行: {}, 结束列: {}",
                    row1, column1, row2, column2);

        //存在动态行数,先检验图片是否在动态行下方
        Map<Integer, Integer> colMap = (Map<Integer, Integer>) dbObject.get("colAddCntMap");

        int colNum = column2 - column1;

        boolean shouldDynamic = false;

        Map<Integer, Integer> picMap = new HashMap<>();

        for (int i = 0; i < colNum; i++) {
            if (colMap.containsKey(column1 + i)){
                shouldDynamic = true;
                picMap.put(column1 + i, colMap.get(column1 + i));
            }
        }

        if (shouldDynamic) {
            int max = 0;
            for (Integer value : picMap.values()) {
                if (value > max) {
                    max = value;
                }
            }
            logger.debug("检测到动态行数，最大偏移量: {}", max);
            logger.debug("动态调整前位置 - 起始行: {}, 结束行: {}", row1, row2);
            row1 += max;
            row2 += max;
            logger.debug("动态调整后位置 - 起始行: {}, 结束行: {}", row1, row2);
        }

        // 记录图片的行列坐标信息，不再直接计算像素位置
        recordImageCoordinates(param, row1, column1, row2, column2);

        return "";
    }

        JSONObject config = dbObject.getJSONObject("config");

        int defColumnLen = 73;
        int defRowLen = 19;
        boolean column = false;
        boolean row = false;
        JSONObject rowlen = null;
        JSONObject columnlen = null;
        if (config != null) {
            rowlen = config.getJSONObject("rowlen");
            columnlen = config.getJSONObject("columnlen");
            if (rowlen != null) {
                row = true;
            }
            if (columnlen != null) {
                column = true;
            }
        }
        int width = 0;
        int left = 0;
        for (int i = 0; i < column2; i++) {
            int c = defColumnLen;
            if (column) {
                Integer colLen = columnlen.getInteger(i + "");
                if (colLen != null) {
                    c = colLen;
                }
            }
            if (i < column1) {
                left += c;
            }
            if (i >= column1) {
                width += c;
            }
        }

        // Debug: 打印计算出的宽度和左边距
        logger.debug("计算出的图片宽度: {}, 左边距: {}", width, left);

        int height = 0;
        int top = 0;

        // 计算顶部距离：累加从第0行到row1-1行的高度
        for (int i = 0; i < row1; i++) {
            int r = defRowLen;
            if (row) {
                Integer rowLen = rowlen.getInteger(i + "");
                if (rowLen != null) {
                    r = rowLen;
                }
            }
            top += r;
            logger.debug("累加第{}行高度{}到顶部距离，当前顶部距离: {}", i, r, top);
        }

        // 计算图片高度：累加从row1行到row2-1行的高度
        for (int i = row1; i < row2; i++) {
            int r = defRowLen;
            if (row) {
                Integer rowLen = rowlen.getInteger(i + "");
                if (rowLen != null) {
                    r = rowLen;
                }
            }
            height += r;
            logger.debug("累加第{}行高度{}到图片高度，当前图片高度: {}", i, r, height);
        }

        // Debug: 打印计算出的高度和顶部距离
        logger.debug("计算出的图片高度: {}, 顶部距离: {}", height, top);


        JSONObject images = dbObject.getJSONObject("images");
        if (images == null) {
            images = new JSONObject();
            dbObject.put("images", images);
        }

        String imageName = "img_" + getRandomString() + "_" + System.currentTimeMillis();
        //String imageName = "img_4Lei05niaei1_1685768723632";

        // Debug: 打印生成的图片名称
        logger.debug("生成的图片名称: {}", imageName);

        JSONObject image = new JSONObject();
        image.put("type", "3");
        image.put("src", param);

        JSONObject def = new JSONObject();
        def.put("width", width);
        def.put("height", height);
        def.put("left", left);
        def.put("top", top);

        // Debug: 打印图片实际渲染位置信息
        logger.debug("图片实际渲染位置 - 宽度: {}, 高度: {}, 左边距: {}, 顶部距离: {}",
                    width, height, left, top);

        JSONObject crop = new JSONObject();
        crop.put("width", width);
        crop.put("height", height);
        crop.put("offsetLeft", 0);
        crop.put("offsetTop", 0);

        JSONObject border = new JSONObject();
        border.put("width", 0);
        border.put("radius", 0);
        border.put("style", "solid");
        border.put("color", "#000");

        image.put("default", def);
        image.put("crop", crop);
        image.put("border", border);
//        image.put("originWidth",720);
//        image.put("originHeight",720);
//        image.put("isFixedPos", false);
//        image.put("fixedLeft", 301);
//        image.put("fixedTop", 310);

        images.put(imageName, image);

        // Debug: 打印完整的图片配置信息
        logger.debug("图片配置完成 - 图片名称: {}, 图片源: {}, 完整配置: {}",
                    imageName, param, image.toJSONString());

        /*images.forEach((s, o) -> {

        });*/

        return "";
    }

    private String getRandomString() {
        String[] code = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"};
        int len = 12;
        StringBuilder randomStr = new StringBuilder();
        for (int i = 0; i < len; i++) {
            int random = new Random().nextInt(code.length);
            randomStr.append(code[random]);
        }
        return randomStr.toString();
    }
}
