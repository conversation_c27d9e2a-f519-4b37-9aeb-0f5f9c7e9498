package com.logictrue.interfaces.service.excel;

import com.alibaba.fastjson.JSONObject;
import com.logictrue.interfaces.domain.vo.ReportLenVO;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.*;
import java.util.*;
import java.util.List;

/**
 * 图片高度计算修复测试类
 * 验证修复后的图片位置计算逻辑是否正确
 */
public class ImageHeightCalculationFixTest {
    
    private static final Logger logger = LoggerFactory.getLogger(ImageHeightCalculationFixTest.class);
    
    @Test
    public void testImagePositionCalculationLogic() {
        logger.info("=== 测试图片位置计算逻辑修复 ===");
        
        // 场景说明：
        // 设计时：第0行=25px, 第1行=30px, 第2行=35px, 第3行=40px
        // 图片位置：第2-4行，第1-3列
        // 原始计算：top = 25+30 = 55px, height = 35+40 = 75px
        // 
        // 自动行高后：第2行变为60px, 第3行变为80px
        // 期望结果：top = 25+30 = 55px (不变), height = 60+80 = 140px (增加)
        
        // 创建原始行高配置（设计时）
        JSONObject originalRowlen = new JSONObject();
        originalRowlen.put("0", 25);
        originalRowlen.put("1", 30);
        originalRowlen.put("2", 35);  // 这行会变化
        originalRowlen.put("3", 40);  // 这行会变化
        originalRowlen.put("4", 45);
        
        // 创建更新后的行高配置（自动计算后）
        JSONObject currentRowlen = new JSONObject();
        currentRowlen.put("0", 25);
        currentRowlen.put("1", 30);
        currentRowlen.put("2", 60);  // 从35增加到60
        currentRowlen.put("3", 80);  // 从40增加到80
        currentRowlen.put("4", 45);
        
        // 创建列宽配置
        JSONObject columnlen = new JSONObject();
        columnlen.put("0", 80);
        columnlen.put("1", 90);
        columnlen.put("2", 100);
        columnlen.put("3", 110);
        
        // 模拟图片原始位置（基于设计时行高计算）
        int originalTop = 55;    // 第0+1行高度 = 25+30 = 55
        int originalHeight = 75; // 第2+3行高度 = 35+40 = 75
        int originalLeft = 80;   // 第0列宽度 = 80
        int originalWidth = 190; // 第1+2列宽度 = 90+100 = 190
        
        logger.info("原始图片位置 - top: {}, height: {}, left: {}, width: {}", 
                   originalTop, originalHeight, originalLeft, originalWidth);
        
        // 测试反推行列范围（应该使用原始行高）
        int[] rowRange = calculateRowRangeFromPixels(originalTop, originalHeight, originalRowlen, 19);
        int[] columnRange = calculateColumnRangeFromPixels(originalLeft, originalWidth, columnlen, 73);
        
        logger.info("反推的行列范围 - 行: {}-{}, 列: {}-{}", rowRange[0], rowRange[1], columnRange[0], columnRange[1]);
        
        // 验证反推结果
        assert rowRange[0] == 2 : "起始行应该是2，实际是" + rowRange[0];
        assert rowRange[1] == 4 : "结束行应该是4，实际是" + rowRange[1];
        assert columnRange[0] == 1 : "起始列应该是1，实际是" + columnRange[0];
        assert columnRange[1] == 3 : "结束列应该是3，实际是" + columnRange[1];
        
        // 测试重新计算位置（应该使用更新后的行高）
        int[] newPosition = recalculateImagePosition(rowRange[0], rowRange[1], columnRange[0], columnRange[1], 
                                                   currentRowlen, columnlen, 19, 73);
        
        int newLeft = newPosition[0];
        int newTop = newPosition[1];
        int newWidth = newPosition[2];
        int newHeight = newPosition[3];
        
        logger.info("重新计算的图片位置 - top: {}, height: {}, left: {}, width: {}", 
                   newTop, newHeight, newLeft, newWidth);
        
        // 验证计算结果
        assert newTop == 55 : "顶部距离应该保持55，实际是" + newTop;
        assert newHeight == 140 : "高度应该是140 (60+80)，实际是" + newHeight;
        assert newLeft == 80 : "左边距应该保持80，实际是" + newLeft;
        assert newWidth == 190 : "宽度应该保持190，实际是" + newWidth;
        
        logger.info("✅ 图片位置计算逻辑修复验证通过！");
        
        // 计算变化量
        int topChange = newTop - originalTop;
        int heightChange = newHeight - originalHeight;
        
        logger.info("位置变化 - 顶部距离变化: {}px, 高度变化: {}px", topChange, heightChange);
        logger.info("期望结果 - 顶部距离变化: 0px (不变), 高度变化: 65px (140-75)");
    }
    
    // 辅助方法：根据像素坐标计算行范围
    private int[] calculateRowRangeFromPixels(int top, int height, JSONObject rowlen, int defRowLen) {
        int currentTop = 0;
        int row1 = -1;
        int row2 = -1;
        
        // 找到起始行
        for (int i = 0; i < 100; i++) {
            int rowHeight = rowlen != null && rowlen.containsKey(String.valueOf(i)) 
                          ? rowlen.getInteger(String.valueOf(i)) : defRowLen;
            
            if (currentTop <= top && currentTop + rowHeight > top) {
                row1 = i;
                break;
            }
            currentTop += rowHeight;
        }
        
        // 找到结束行
        if (row1 >= 0) {
            int targetBottom = top + height;
            currentTop = 0;
            for (int i = 0; i < 100; i++) {
                int rowHeight = rowlen != null && rowlen.containsKey(String.valueOf(i)) 
                              ? rowlen.getInteger(String.valueOf(i)) : defRowLen;
                currentTop += rowHeight;
                
                if (currentTop >= targetBottom) {
                    row2 = i + 1;
                    break;
                }
            }
        }
        
        return new int[]{Math.max(0, row1), Math.max(1, row2)};
    }
    
    // 辅助方法：根据像素坐标计算列范围
    private int[] calculateColumnRangeFromPixels(int left, int width, JSONObject columnlen, int defColumnLen) {
        int currentLeft = 0;
        int column1 = -1;
        int column2 = -1;
        
        // 找到起始列
        for (int i = 0; i < 100; i++) {
            int columnWidth = columnlen != null && columnlen.containsKey(String.valueOf(i)) 
                            ? columnlen.getInteger(String.valueOf(i)) : defColumnLen;
            
            if (currentLeft <= left && currentLeft + columnWidth > left) {
                column1 = i;
                break;
            }
            currentLeft += columnWidth;
        }
        
        // 找到结束列
        if (column1 >= 0) {
            int targetRight = left + width;
            currentLeft = 0;
            for (int i = 0; i < 100; i++) {
                int columnWidth = columnlen != null && columnlen.containsKey(String.valueOf(i)) 
                                ? columnlen.getInteger(String.valueOf(i)) : defColumnLen;
                currentLeft += columnWidth;
                
                if (currentLeft >= targetRight) {
                    column2 = i + 1;
                    break;
                }
            }
        }
        
        return new int[]{Math.max(0, column1), Math.max(1, column2)};
    }
    
    // 辅助方法：重新计算图片位置
    private int[] recalculateImagePosition(int row1, int row2, int column1, int column2, 
                                         JSONObject rowlen, JSONObject columnlen, 
                                         int defRowLen, int defColumnLen) {
        // 计算新的左边距和宽度
        int width = 0;
        int left = 0;
        for (int i = 0; i < column2; i++) {
            int c = columnlen != null && columnlen.containsKey(String.valueOf(i)) 
                  ? columnlen.getInteger(String.valueOf(i)) : defColumnLen;
            
            if (i < column1) {
                left += c;
            }
            if (i >= column1) {
                width += c;
            }
        }

        // 计算新的顶部距离和高度
        int height = 0;
        int top = 0;
        for (int i = 0; i < row2; i++) {
            int r = rowlen != null && rowlen.containsKey(String.valueOf(i)) 
                  ? rowlen.getInteger(String.valueOf(i)) : defRowLen;
            
            if (i < row1) {
                top += r;
            }
            if (i >= row1) {
                height += r;
            }
        }

        return new int[]{left, top, width, height};
    }
    
    @Test
    public void testEdgeCases() {
        logger.info("=== 测试边界情况 ===");
        
        // 测试图片在第一行的情况
        JSONObject rowlen = new JSONObject();
        rowlen.put("0", 50); // 第0行从19增加到50
        rowlen.put("1", 30);
        
        // 图片位置：第0-1行
        int[] rowRange = calculateRowRangeFromPixels(0, 19, createOriginalRowlen(), 19);
        int[] newPosition = recalculateImagePosition(rowRange[0], rowRange[1], 0, 1, rowlen, createColumnlen(), 19, 73);
        
        logger.info("第一行图片测试 - 原始高度: 19, 新高度: {}", newPosition[3]);
        
        // 测试图片跨越多行但只有部分行变化的情况
        JSONObject partialChangeRowlen = new JSONObject();
        partialChangeRowlen.put("0", 25);
        partialChangeRowlen.put("1", 60); // 只有第1行变化
        partialChangeRowlen.put("2", 35);
        partialChangeRowlen.put("3", 40);
        
        // 图片位置：第1-4行
        int[] multiRowRange = calculateRowRangeFromPixels(25, 105, createOriginalRowlen(), 19); // 25 + 30 + 35 + 40 = 130
        int[] multiRowNewPosition = recalculateImagePosition(multiRowRange[0], multiRowRange[1], 0, 1, 
                                                           partialChangeRowlen, createColumnlen(), 19, 73);
        
        logger.info("部分行变化测试 - 原始高度: 105, 新高度: {}", multiRowNewPosition[3]);
        
        logger.info("=== 边界情况测试完成 ===");
    }
    
    private JSONObject createOriginalRowlen() {
        JSONObject rowlen = new JSONObject();
        rowlen.put("0", 25);
        rowlen.put("1", 30);
        rowlen.put("2", 35);
        rowlen.put("3", 40);
        rowlen.put("4", 45);
        return rowlen;
    }
    
    private JSONObject createColumnlen() {
        JSONObject columnlen = new JSONObject();
        columnlen.put("0", 80);
        columnlen.put("1", 90);
        columnlen.put("2", 100);
        columnlen.put("3", 110);
        return columnlen;
    }
}
