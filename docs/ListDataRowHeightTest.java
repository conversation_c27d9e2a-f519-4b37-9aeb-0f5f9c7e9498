package com.logictrue.interfaces.service.excel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.logictrue.interfaces.domain.dto.DataSetDto;
import com.logictrue.interfaces.domain.dto.OriginalDataDto;
import com.logictrue.interfaces.domain.vo.PreviewVO;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * List数据行高测试类
 * 用于验证list类型数据设计时行高和实际渲染时行高的debug日志
 */
public class ListDataRowHeightTest {
    
    private static final Logger logger = LoggerFactory.getLogger(ListDataRowHeightTest.class);
    
    @Test
    public void testListDataRowHeight() {
        // 创建测试用的DynamicCellService和ColumnAndRowService
        DynamicCellService dynamicCellService = new DynamicCellService();
        ColumnAndRowService columnAndRowService = new ColumnAndRowService();
        
        // 创建测试数据
        JSONObject dbObject = createTestDbObject();
        PreviewVO previewVO = createTestPreviewVO();
        DataSetDto dataSet = createTestDataSet();
        
        // 模拟单元格字符串
        String cellStr = createTestCellStr();
        
        // 测试参数
        String v = "#{testDataSet.info.testField}";
        int cnt = 0;  // 动态行偏移
        int rnt = 0;  // 动态列偏移
        int r = 2;    // 原始行
        int c = 1;    // 原始列
        
        JSONObject merge = new JSONObject();
        Map<Integer, Integer> colAddCntMap = new HashMap<>();
        Map<Integer, Integer> rowAddCntMap = new HashMap<>();
        Map<Integer, List<Object>> rowLenMap = new HashMap<>();
        
        logger.info("=== 开始测试List数据行高设置 ===");
        logger.info("测试场景：设计时配置了行高，list数据中部分项目有自定义高度");
        
        // 执行测试
        dynamicCellService.handleDynamicCellObject(
            dataSet, previewVO, v, cellStr, cnt, rnt, r, c, 
            merge, dbObject, colAddCntMap, rowAddCntMap, rowLenMap
        );
        
        logger.info("=== 测试完成 ===");
        
        // 验证结果
        JSONObject config = dbObject.getJSONObject("config");
        JSONObject newRowlen = config.getJSONObject("rowlen");
        JSONObject customHeight = config.getJSONObject("customHeight");
        
        logger.info("最终行高配置:");
        if (newRowlen != null) {
            newRowlen.forEach((key, value) -> {
                logger.info("第{}行: {}px", key, value);
            });
        }
        
        logger.info("自定义行高标记:");
        if (customHeight != null) {
            customHeight.forEach((key, value) -> {
                logger.info("第{}行: 自定义={}", key, value);
            });
        }
    }
    
    private JSONObject createTestDbObject() {
        JSONObject dbObject = new JSONObject();
        
        // 设置原始行高配置（设计时配置）
        JSONObject rowlen = new JSONObject();
        rowlen.put("0", 25);  // 第0行高度25
        rowlen.put("1", 30);  // 第1行高度30
        rowlen.put("2", 35);  // 第2行高度35（测试行）
        rowlen.put("3", 40);  // 第3行高度40
        dbObject.put("rowlen", rowlen);
        
        // 设置配置对象
        JSONObject config = new JSONObject();
        JSONObject newRowlen = new JSONObject();
        JSONObject customHeight = new JSONObject();
        
        // 复制原始行高到新配置中
        newRowlen.putAll(rowlen);
        
        config.put("rowlen", newRowlen);
        config.put("customHeight", customHeight);
        dbObject.put("config", config);
        
        // 设置单元格数据数组
        dbObject.put("celldata", new JSONArray());
        
        // 设置计算映射
        dbObject.put("calcMap", new JSONObject());
        
        return dbObject;
    }
    
    private PreviewVO createTestPreviewVO() {
        PreviewVO previewVO = new PreviewVO();
        
        // 创建测试数据
        Map<String, OriginalDataDto> results = new HashMap<>();
        OriginalDataDto originalDataDto = new OriginalDataDto();
        
        // 创建list数据，包含不同的高度配置
        List<Object> testData = new ArrayList<>();
        
        // 第一个数据对象 - 使用默认行高
        JSONObject data1 = new JSONObject();
        JSONObject info1 = new JSONObject();
        info1.put("testField", "数据项1 - 使用默认行高");
        data1.put("info", info1);
        testData.add(data1);
        
        // 第二个数据对象 - 指定自定义行高
        JSONObject data2 = new JSONObject();
        JSONObject info2 = new JSONObject();
        info2.put("testField", "数据项2 - 自定义行高50");
        info2.put("height", 50);  // 自定义行高
        data2.put("info", info2);
        testData.add(data2);
        
        // 第三个数据对象 - 使用默认行高
        JSONObject data3 = new JSONObject();
        JSONObject info3 = new JSONObject();
        info3.put("testField", "数据项3 - 使用默认行高");
        data3.put("info", info3);
        testData.add(data3);
        
        // 第四个数据对象 - 指定自定义行高
        JSONObject data4 = new JSONObject();
        JSONObject info4 = new JSONObject();
        info4.put("testField", "数据项4 - 自定义行高60");
        info4.put("height", 60);  // 自定义行高
        data4.put("info", info4);
        testData.add(data4);
        
        originalDataDto.setData(testData);
        results.put("testDataSet", originalDataDto);
        
        previewVO.setResults(results);
        return previewVO;
    }
    
    private DataSetDto createTestDataSet() {
        DataSetDto dataSet = new DataSetDto();
        dataSet.setSetCode("testDataSet");
        dataSet.setFieldLabel("info.testField");
        return dataSet;
    }
    
    private String createTestCellStr() {
        JSONObject cellData = new JSONObject();
        cellData.put("r", 2);
        cellData.put("c", 1);
        
        JSONObject v = new JSONObject();
        v.put("v", "");
        v.put("m", "");
        v.put("ct", new JSONObject());
        
        cellData.put("v", v);
        
        return cellData.toJSONString();
    }
    
    @Test
    public void testColumnAndRowServiceDirectly() {
        logger.info("=== 直接测试ColumnAndRowService ===");
        
        ColumnAndRowService service = new ColumnAndRowService();
        JSONObject dbObject = createTestDbObject();
        
        // 测试setRowLen方法
        logger.info("测试setRowLen方法:");
        service.setRowLen(dbObject, 2, 1, 0, 0);  // 第一个数据项
        service.setRowLen(dbObject, 2, 1, 0, 1);  // 第二个数据项
        
        // 测试setCustomRowLen方法
        logger.info("测试setCustomRowLen方法:");
        service.setCustomRowLen(dbObject, 2, 1, 0, 2, 55);  // 第三个数据项，自定义高度55
        
        // 测试setListHeight方法
        logger.info("测试setListHeight方法:");
        service.setListHeight(45, dbObject, 2, 1, 0, 3);  // 第四个数据项，高度对象为Integer
        service.setListHeight("default", dbObject, 2, 1, 0, 4);  // 第五个数据项，高度对象为String
        
        logger.info("=== 直接测试完成 ===");
    }
}
