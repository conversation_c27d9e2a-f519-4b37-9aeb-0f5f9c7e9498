package com.logictrue.interfaces.strategy.param;

import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * PicParam 测试类
 * 用于验证图片位置计算的正确性，特别是顶部距离计算
 */
public class PicParamTest {
    
    private static final Logger logger = LoggerFactory.getLogger(PicParamTest.class);
    
    @Test
    public void testPicPositionCalculation() {
        // 创建测试数据
        JSONObject dbObject = new JSONObject();
        
        // 模拟没有动态行数的情况
        dbObject.put("colAddCntMap", new JSONObject());
        
        // 模拟配置信息 - 自定义行高和列宽
        JSONObject config = new JSONObject();
        JSONObject rowlen = new JSONObject();
        JSONObject columnlen = new JSONObject();
        
        // 设置前5行的行高分别为：20, 25, 30, 35, 40
        rowlen.put("0", 20);
        rowlen.put("1", 25);
        rowlen.put("2", 30);
        rowlen.put("3", 35);
        rowlen.put("4", 40);
        
        // 设置前5列的列宽分别为：80, 90, 100, 110, 120
        columnlen.put("0", 80);
        columnlen.put("1", 90);
        columnlen.put("2", 100);
        columnlen.put("3", 110);
        columnlen.put("4", 120);
        
        config.put("rowlen", rowlen);
        config.put("columnlen", columnlen);
        dbObject.put("config", config);
        
        // 创建PicParam实例
        PicParam picParam = new PicParam(dbObject);
        
        // 测试用例1：图片位置在第2-4行，第1-3列 (row1=2, column1=1, row2=4, column2=3)
        String pattern1 = "test#2-1-4-3";
        String param1 = "test-image-1.jpg";
        
        logger.info("=== 测试用例1：图片位置在第2-4行，第1-3列 ===");
        logger.info("期望顶部距离 = 第0行高度 + 第1行高度 = 20 + 25 = 45");
        logger.info("期望图片高度 = 第2行高度 + 第3行高度 = 30 + 35 = 65");
        logger.info("期望左边距 = 第0列宽度 = 80");
        logger.info("期望图片宽度 = 第1列宽度 + 第2列宽度 = 90 + 100 = 190");
        
        picParam.format(param1, pattern1);
        
        // 测试用例2：图片位置在第0-2行，第0-2列 (从第一行第一列开始)
        String pattern2 = "test#0-0-2-2";
        String param2 = "test-image-2.jpg";
        
        logger.info("\n=== 测试用例2：图片位置在第0-2行，第0-2列 ===");
        logger.info("期望顶部距离 = 0 (从第一行开始)");
        logger.info("期望图片高度 = 第0行高度 + 第1行高度 = 20 + 25 = 45");
        logger.info("期望左边距 = 0 (从第一列开始)");
        logger.info("期望图片宽度 = 第0列宽度 + 第1列宽度 = 80 + 90 = 170");
        
        picParam.format(param2, pattern2);
        
        // 测试用例3：图片位置在第3-5行，第2-4列
        String pattern3 = "test#3-2-5-4";
        String param3 = "test-image-3.jpg";
        
        logger.info("\n=== 测试用例3：图片位置在第3-5行，第2-4列 ===");
        logger.info("期望顶部距离 = 第0+1+2行高度 = 20 + 25 + 30 = 75");
        logger.info("期望图片高度 = 第3+4行高度 = 35 + 40 = 75");
        logger.info("期望左边距 = 第0+1列宽度 = 80 + 90 = 170");
        logger.info("期望图片宽度 = 第2+3列宽度 = 100 + 110 = 210");
        
        picParam.format(param3, pattern3);
    }
    
    @Test
    public void testPicPositionWithDefaultSize() {
        // 测试使用默认行高列宽的情况
        JSONObject dbObject = new JSONObject();
        dbObject.put("colAddCntMap", new JSONObject());
        
        PicParam picParam = new PicParam(dbObject);
        
        // 测试用例：图片位置在第1-3行，第1-3列，使用默认尺寸
        String pattern = "test#1-1-3-3";
        String param = "test-image-default.jpg";
        
        logger.info("\n=== 测试默认尺寸情况：图片位置在第1-3行，第1-3列 ===");
        logger.info("默认行高: 19, 默认列宽: 73");
        logger.info("期望顶部距离 = 第0行高度 = 19");
        logger.info("期望图片高度 = 第1+2行高度 = 19 + 19 = 38");
        logger.info("期望左边距 = 第0列宽度 = 73");
        logger.info("期望图片宽度 = 第1+2列宽度 = 73 + 73 = 146");
        
        picParam.format(param, pattern);
    }
}
