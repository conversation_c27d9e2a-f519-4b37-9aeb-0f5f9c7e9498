# List数据行高Debug日志说明

## 概述

为了更好地调试和监控list类型数据的行高设置过程，我们在相关的服务类中添加了详细的debug日志，用于打印设计时配置的行高和实际渲染时的行高。

## 涉及的文件

### 1. ColumnAndRowService.java
负责具体的行高设置逻辑，包含以下关键方法：
- `setRowLen()` - 设置默认行高
- `setCustomRowLen()` - 设置自定义行高
- `setListHeight()` - 根据数据类型选择行高设置方式
- `setHorizontalHeight()` - 设置横向扩展数据的行高

### 2. DynamicCellService.java
负责处理动态单元格数据，包含list数据的处理逻辑

## Debug日志详情

### ColumnAndRowService 中的日志

#### 1. setRowLen() 方法
```java
logger.debug("设置list数据行高 - 原始行: {}, 列: {}, 动态行偏移: {}, 数据索引: {}", r, c, cnt, i);
logger.debug("设计时配置的行高 - 第{}行: {}", r, rLen);
logger.debug("实际渲染行高设置 - 第{}行: {} (原始行{} + 动态偏移{} + 数据索引{})", 
           actualRow, rLen, r, cnt, i);
logger.debug("移除重复的原始行高配置 - 第{}行", r);
logger.debug("未找到设计时行高配置，使用默认行高");
```

#### 2. setCustomRowLen() 方法
```java
logger.debug("设置list数据自定义行高 - 原始行: {}, 列: {}, 动态行偏移: {}, 数据索引: {}, 自定义高度: {}", 
            r, c, cnt, i, height);
logger.debug("设计时配置的原始行高 - 第{}行: {}", r, rLen);
logger.debug("实际渲染自定义行高设置 - 第{}行: {} (覆盖原始高度{}, 原始行{} + 动态偏移{} + 数据索引{})", 
           actualRow, height, rLen, r, cnt, i);
logger.debug("未找到设计时行高配置，直接设置自定义行高: {}", height);
```

#### 3. setListHeight() 方法
```java
logger.debug("设置list数据行高 - 原始行: {}, 列: {}, 动态行偏移: {}, 数据索引: {}, 高度对象: {}", 
            r, c, cnt, i, height);
logger.debug("使用数据中指定的自定义行高: {}", height);
logger.debug("使用设计时配置的默认行高");
```

### DynamicCellService 中的日志

#### 1. 处理list数据时
```java
logger.debug("处理list类型数据 - 数据集: {}, 字段: {}, 数据量: {}, 原始行: {}, 列: {}", 
           dataSet.getSetCode(), name, len, r, c);
logger.debug("处理list数据第{}项 - 模式: {}, 数据: {}", i, model, o1);
logger.debug("处理list数据项 - 索引: {}, 单元格值: {}, 高度配置: {}", i, cellValue, height);
logger.debug("使用数据中的自定义行高: {}", height);
logger.debug("使用设计时的默认行高配置");
```

#### 2. 普通渲染模式
```java
logger.debug("普通渲染模式 - 数据集: {}, 数据量: {}, 原始行: {}, 列: {}", 
           dataSet.getSetCode(), cellDynamicData.size(), r, c);
logger.debug("普通渲染模式处理数据项 - 索引: {}, 字段: {}, 单元格值: {}", j, pName, cellValue);
```

## 日志信息解释

### 关键参数说明

- **原始行 (r)**: 设计时配置的起始行号
- **列 (c)**: 设计时配置的列号
- **动态行偏移 (cnt)**: 由于前面的动态数据导致的行偏移量
- **数据索引 (i)**: 当前处理的list数据项的索引
- **实际渲染行**: 计算公式为 `原始行 + 动态行偏移 + 数据索引`

### 行高设置逻辑

1. **设计时行高**: 在模板设计时为每一行配置的高度，存储在 `dbObject.rowlen` 中
2. **实际渲染行高**: 根据list数据的实际位置计算出的最终行高，存储在 `dbObject.config.rowlen` 中
3. **自定义行高**: list数据中每个数据项可以通过 `height` 字段指定自定义行高

## 使用示例

### 启用Debug日志

在日志配置文件中添加以下配置：

```xml
<!-- logback.xml -->
<logger name="com.logictrue.interfaces.service.excel.ColumnAndRowService" level="DEBUG"/>
<logger name="com.logictrue.interfaces.service.excel.DynamicCellService" level="DEBUG"/>
```

### 典型的日志输出示例

```
DEBUG - 处理list类型数据 - 数据集: userList, 字段: info.name, 数据量: 3, 原始行: 5, 列: 2
DEBUG - 处理list数据第0项 - 模式: null, 数据: {"name":"张三","height":45}
DEBUG - 处理list数据项 - 索引: 0, 单元格值: 张三, 高度配置: 45
DEBUG - 使用数据中的自定义行高: 45
DEBUG - 设置list数据自定义行高 - 原始行: 5, 列: 2, 动态行偏移: 0, 数据索引: 0, 自定义高度: 45
DEBUG - 设计时配置的原始行高 - 第5行: 30
DEBUG - 实际渲染自定义行高设置 - 第5行: 45 (覆盖原始高度30, 原始行5 + 动态偏移0 + 数据索引0)

DEBUG - 处理list数据第1项 - 模式: null, 数据: {"name":"李四"}
DEBUG - 处理list数据项 - 索引: 1, 单元格值: 李四, 高度配置: null
DEBUG - 使用设计时的默认行高配置
DEBUG - 设置list数据行高 - 原始行: 5, 列: 2, 动态行偏移: 0, 数据索引: 1
DEBUG - 设计时配置的行高 - 第5行: 30
DEBUG - 实际渲染行高设置 - 第6行: 30 (原始行5 + 动态偏移0 + 数据索引1)
```

## 测试验证

创建了 `ListDataRowHeightTest.java` 测试文件，包含：

1. **完整场景测试**: 模拟包含自定义行高和默认行高的list数据处理
2. **直接方法测试**: 直接测试 `ColumnAndRowService` 的各个方法
3. **数据验证**: 验证最终的行高配置是否正确

## 故障排查指南

### 常见问题

1. **行高设置不生效**
   - 检查日志中是否有 "未找到设计时行高配置" 的提示
   - 确认 `dbObject.rowlen` 中是否包含对应行的配置

2. **自定义行高被忽略**
   - 检查数据中的 `height` 字段是否为 `Integer` 类型
   - 查看日志中是否有 "使用数据中的自定义行高" 的提示

3. **行号计算错误**
   - 关注日志中的实际渲染行计算过程
   - 验证 `原始行 + 动态行偏移 + 数据索引` 的计算结果

### 调试步骤

1. 启用DEBUG级别日志
2. 运行包含list数据的报表生成
3. 查看日志输出，重点关注：
   - 设计时行高配置
   - 实际渲染行高设置
   - 行号计算过程
4. 对比预期结果与实际结果

## 总结

通过这些详细的debug日志，开发人员可以：

1. **追踪行高设置过程**: 从设计时配置到实际渲染的完整过程
2. **定位问题根源**: 快速识别是设计时配置问题还是数据处理问题
3. **验证计算逻辑**: 确认行号和行高的计算是否正确
4. **优化性能**: 识别不必要的行高设置操作

这些日志信息将大大提高list数据行高相关问题的调试效率。
