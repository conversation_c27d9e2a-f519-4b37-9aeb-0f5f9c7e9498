# 图片高度自动调整优化说明

## 概述

当报表中使用了自动行高功能时，某些行的高度可能会因为文本内容的自动换行而发生变化。这种变化会影响到图片的显示位置和尺寸，因此需要在自动行高计算完成后，重新调整图片的高度和位置。

## 问题背景

### 原始问题
1. **设计时配置**：图片在设计时根据固定的行高和列宽计算位置和尺寸
2. **运行时变化**：当启用自动行高后，某些行的高度会根据内容自动调整
3. **显示错位**：图片仍然使用设计时的尺寸，导致与实际单元格位置不匹配

### 影响范围
- 图片的垂直位置可能偏移
- 图片的高度可能不再匹配对应的单元格区域
- 整体报表布局可能出现错乱

## 解决方案

### 核心思路
1. **监控行高变化**：在自动行高计算过程中记录哪些行的高度发生了变化
2. **识别受影响图片**：根据图片的位置信息，判断哪些图片会受到行高变化的影响
3. **重新计算位置**：基于新的行高配置，重新计算图片的位置和尺寸
4. **更新图片配置**：将新的位置和尺寸信息更新到图片配置中

### 实现细节

#### 1. 行高变化监控
在 `AnalysisSheetService.analysisRowLenMap()` 方法中：

```java
// 记录行高变化情况，用于后续图片高度调整
Map<Integer, Integer> rowHeightChanges = new HashMap<>();

rowLenMap.forEach((row, reportLenVOS) -> {
    // ... 计算新行高 ...
    
    // 记录行高变化
    Integer originalHeight = finalRowlen.getInteger(String.valueOf(row));
    if (originalHeight == null) {
        originalHeight = 19; // 默认行高
    }
    if (finalHeight != originalHeight) {
        rowHeightChanges.put(row, finalHeight - originalHeight);
    }
    
    finalRowlen.put(String.valueOf(row), finalHeight);
});

// 如果有行高变化，需要重新计算图片高度
if (!rowHeightChanges.isEmpty()) {
    recalculateImageHeights(dbObject, rowHeightChanges);
}
```

#### 2. 图片位置反推算法
由于图片配置中只有像素坐标，需要反推出对应的行列范围：

```java
private int[] calculateRowRangeFromPixels(int top, int height, JSONObject rowlen, int defRowLen) {
    int currentTop = 0;
    int row1 = -1, row2 = -1;
    
    // 找到起始行
    for (int i = 0; i < 1000; i++) {
        int rowHeight = rowlen != null && rowlen.containsKey(String.valueOf(i)) 
                      ? rowlen.getInteger(String.valueOf(i)) : defRowLen;
        
        if (currentTop <= top && currentTop + rowHeight > top) {
            row1 = i;
            break;
        }
        currentTop += rowHeight;
    }
    
    // 找到结束行
    // ... 类似逻辑 ...
    
    return new int[]{row1, row2};
}
```

#### 3. 图片高度重新计算
```java
private void recalculateImageHeights(JSONObject dbObject, Map<Integer, Integer> rowHeightChanges) {
    JSONObject images = dbObject.getJSONObject("images");
    
    images.forEach((imageName, imageObj) -> {
        JSONObject image = (JSONObject) imageObj;
        JSONObject defaultConfig = image.getJSONObject("default");
        
        // 获取原始位置信息
        int originalTop = defaultConfig.getInteger("top");
        int originalHeight = defaultConfig.getInteger("height");
        
        // 反推行列范围
        int[] rowRange = calculateRowRangeFromPixels(originalTop, originalHeight, rowlen, defRowLen);
        
        // 检查是否受影响
        boolean needRecalculate = false;
        for (int row = rowRange[0]; row < rowRange[1]; row++) {
            if (rowHeightChanges.containsKey(row)) {
                needRecalculate = true;
                break;
            }
        }
        
        if (needRecalculate) {
            // 重新计算位置和尺寸
            int[] newPosition = recalculateImagePosition(rowRange[0], rowRange[1], 
                                                       columnRange[0], columnRange[1], 
                                                       rowlen, columnlen, defRowLen, defColumnLen);
            
            // 更新配置
            defaultConfig.put("left", newPosition[0]);
            defaultConfig.put("top", newPosition[1]);
            defaultConfig.put("width", newPosition[2]);
            defaultConfig.put("height", newPosition[3]);
        }
    });
}
```

## Debug日志

### 新增的日志信息

1. **行高变化监控**：
```
DEBUG - 行高设置 - 第2行: 原始高度=35, 自动计算高度=62, 最终高度=62
DEBUG - 行高发生变化 - 第2行: 原始高度=35, 新高度=62, 变化量=27
```

2. **图片处理过程**：
```
DEBUG - 开始重新计算图片高度，共2张图片需要处理
DEBUG - 处理图片img_test1_123456 - 原始位置: left=80, top=55, width=190, height=75
DEBUG - 图片img_test1_123456推算的行列范围 - 行: 2-4, 列: 1-3
DEBUG - 图片img_test1_123456涉及的第2行高度发生变化: 27
DEBUG - 图片img_test1_123456重新计算后的位置 - left=80, top=55, width=190, height=102
```

3. **跳过处理的情况**：
```
DEBUG - 图片img_test2_789012涉及的行高度未发生变化，无需调整
```

## 测试验证

### 测试场景

1. **基本功能测试**：
   - 创建包含图片的报表模板
   - 设置部分单元格为自动换行
   - 验证图片位置和尺寸是否正确调整

2. **边界情况测试**：
   - 图片跨越多行，部分行高度变化
   - 图片位置在变化行的边界
   - 没有图片的情况

3. **性能测试**：
   - 大量图片的处理性能
   - 复杂行高配置的计算效率

### 测试用例

创建了 `ImageHeightOptimizationTest.java` 测试文件，包含：

1. **完整流程测试**：模拟自动行高计算和图片调整的完整过程
2. **算法单元测试**：测试行列范围计算的准确性
3. **配置验证测试**：验证图片配置更新的正确性

## 配置要求

### 启用Debug日志
```xml
<logger name="com.logictrue.interfaces.service.excel.AnalysisSheetService" level="DEBUG"/>
```

### 数据结构要求
- 图片配置必须包含 `default` 对象，包含 `left`、`top`、`width`、`height` 属性
- 行高配置在 `config.rowlen` 中
- 列宽配置在 `config.columnlen` 中

## 注意事项

### 性能考虑
1. **计算复杂度**：行列范围反推的时间复杂度为 O(n)，其中 n 为最大行列数
2. **内存使用**：需要额外存储行高变化信息
3. **处理时机**：在所有自动行高计算完成后统一处理图片

### 兼容性
1. **向后兼容**：不影响现有的图片处理逻辑
2. **配置兼容**：支持缺少部分配置信息的情况
3. **错误处理**：对异常情况进行适当的日志记录和跳过处理

### 限制条件
1. **最大行列数**：当前限制为1000行/列，可根据需要调整
2. **精度限制**：像素坐标反推可能存在微小误差
3. **复杂布局**：对于非规则的图片布局可能需要特殊处理

## 总结

通过这个优化方案，我们实现了：

1. **自动适应**：图片能够自动适应行高变化
2. **精确计算**：基于实际行高重新计算图片位置
3. **性能优化**：只处理受影响的图片
4. **调试友好**：提供详细的debug日志
5. **向后兼容**：不影响现有功能

这个优化确保了在使用自动行高功能时，图片能够保持正确的位置和尺寸，提升了报表的整体显示效果。
