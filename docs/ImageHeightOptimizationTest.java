package com.logictrue.interfaces.service.excel;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.logictrue.interfaces.domain.vo.ReportLenVO;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.*;
import java.util.*;
import java.util.List;

/**
 * 图片高度优化测试类
 * 用于验证当使用自动行高时，图片高度能够正确重新计算
 */
public class ImageHeightOptimizationTest {
    
    private static final Logger logger = LoggerFactory.getLogger(ImageHeightOptimizationTest.class);
    
    @Test
    public void testImageHeightRecalculation() {
        // 创建测试用的AnalysisSheetService
        AnalysisSheetService analysisSheetService = new AnalysisSheetService();
        
        // 创建测试数据
        JSONObject dbObject = createTestDbObjectWithImages();
        Map<Integer, List<ReportLenVO>> rowLenMap = createTestRowLenMap();
        
        logger.info("=== 开始测试图片高度重新计算 ===");
        
        // 打印原始图片配置
        printImageConfigurations(dbObject, "原始");
        
        // 执行自动行高分析（这会触发图片高度重新计算）
        try {
            // 使用反射调用私有方法进行测试
            java.lang.reflect.Method method = AnalysisSheetService.class.getDeclaredMethod(
                "analysisRowLenMap", Map.class, JSONObject.class);
            method.setAccessible(true);
            method.invoke(analysisSheetService, rowLenMap, dbObject);
        } catch (Exception e) {
            logger.error("调用analysisRowLenMap方法失败", e);
        }
        
        // 打印调整后的图片配置
        printImageConfigurations(dbObject, "调整后");
        
        // 验证行高配置
        printRowHeightConfigurations(dbObject);
        
        logger.info("=== 图片高度重新计算测试完成 ===");
    }
    
    private JSONObject createTestDbObjectWithImages() {
        JSONObject dbObject = new JSONObject();
        
        // 设置配置信息
        JSONObject config = new JSONObject();
        
        // 设置原始行高配置
        JSONObject rowlen = new JSONObject();
        rowlen.put("0", 25);  // 第0行
        rowlen.put("1", 30);  // 第1行
        rowlen.put("2", 35);  // 第2行（图片涉及的行）
        rowlen.put("3", 40);  // 第3行（图片涉及的行）
        rowlen.put("4", 45);  // 第4行
        
        // 设置列宽配置
        JSONObject columnlen = new JSONObject();
        columnlen.put("0", 80);   // 第0列
        columnlen.put("1", 90);   // 第1列（图片涉及的列）
        columnlen.put("2", 100);  // 第2列（图片涉及的列）
        columnlen.put("3", 110);  // 第3列
        
        config.put("rowlen", rowlen);
        config.put("columnlen", columnlen);
        dbObject.put("config", config);
        
        // 创建图片配置
        JSONObject images = new JSONObject();
        
        // 图片1：位置在第2-4行，第1-3列
        JSONObject image1 = new JSONObject();
        image1.put("type", "3");
        image1.put("src", "test-image-1.jpg");
        
        JSONObject default1 = new JSONObject();
        // 根据原始行高列宽计算的位置
        // left = 第0列宽度 = 80
        // top = 第0+1行高度 = 25 + 30 = 55
        // width = 第1+2列宽度 = 90 + 100 = 190
        // height = 第2+3行高度 = 35 + 40 = 75
        default1.put("left", 80);
        default1.put("top", 55);
        default1.put("width", 190);
        default1.put("height", 75);
        
        JSONObject crop1 = new JSONObject();
        crop1.put("width", 190);
        crop1.put("height", 75);
        crop1.put("offsetLeft", 0);
        crop1.put("offsetTop", 0);
        
        image1.put("default", default1);
        image1.put("crop", crop1);
        
        images.put("img_test1_" + System.currentTimeMillis(), image1);
        
        // 图片2：位置在第0-2行，第0-2列（不会受到自动行高影响）
        JSONObject image2 = new JSONObject();
        image2.put("type", "3");
        image2.put("src", "test-image-2.jpg");
        
        JSONObject default2 = new JSONObject();
        // left = 0, top = 0, width = 80 + 90 = 170, height = 25 + 30 = 55
        default2.put("left", 0);
        default2.put("top", 0);
        default2.put("width", 170);
        default2.put("height", 55);
        
        JSONObject crop2 = new JSONObject();
        crop2.put("width", 170);
        crop2.put("height", 55);
        crop2.put("offsetLeft", 0);
        crop2.put("offsetTop", 0);
        
        image2.put("default", default2);
        image2.put("crop", crop2);
        
        images.put("img_test2_" + System.currentTimeMillis(), image2);
        
        dbObject.put("images", images);
        
        return dbObject;
    }
    
    private Map<Integer, List<ReportLenVO>> createTestRowLenMap() {
        Map<Integer, List<ReportLenVO>> rowLenMap = new HashMap<>();
        
        // 第2行有自动换行的内容，需要增加高度
        List<ReportLenVO> row2List = new ArrayList<>();
        ReportLenVO reportLenVO2 = new ReportLenVO();
        reportLenVO2.setRow(2);
        reportLenVO2.setCol(1);
        reportLenVO2.setFont(new Font("宋体", Font.PLAIN, 12));
        reportLenVO2.setColLen(90);
        reportLenVO2.setRowLen(35); // 原始行高
        reportLenVO2.setCellValue("这是一段很长的文本内容，需要自动换行显示，因此会增加行高。这段文本足够长，可以触发自动换行计算。");
        row2List.add(reportLenVO2);
        rowLenMap.put(2, row2List);
        
        // 第3行也有自动换行的内容
        List<ReportLenVO> row3List = new ArrayList<>();
        ReportLenVO reportLenVO3 = new ReportLenVO();
        reportLenVO3.setRow(3);
        reportLenVO3.setCol(2);
        reportLenVO3.setFont(new Font("宋体", Font.PLAIN, 14));
        reportLenVO3.setColLen(100);
        reportLenVO3.setRowLen(40); // 原始行高
        reportLenVO3.setCellValue("另一段需要自动换行的长文本内容，用于测试图片高度的重新计算功能。");
        row3List.add(reportLenVO3);
        rowLenMap.put(3, row3List);
        
        return rowLenMap;
    }
    
    private void printImageConfigurations(JSONObject dbObject, String stage) {
        logger.info("=== {}图片配置 ===", stage);
        JSONObject images = dbObject.getJSONObject("images");
        if (images != null) {
            images.forEach((imageName, imageObj) -> {
                JSONObject image = (JSONObject) imageObj;
                JSONObject defaultConfig = image.getJSONObject("default");
                if (defaultConfig != null) {
                    logger.info("图片{}: left={}, top={}, width={}, height={}", 
                              imageName,
                              defaultConfig.getInteger("left"),
                              defaultConfig.getInteger("top"),
                              defaultConfig.getInteger("width"),
                              defaultConfig.getInteger("height"));
                }
            });
        }
    }
    
    private void printRowHeightConfigurations(JSONObject dbObject) {
        logger.info("=== 最终行高配置 ===");
        JSONObject config = dbObject.getJSONObject("config");
        JSONObject rowlen = config.getJSONObject("rowlen");
        if (rowlen != null) {
            rowlen.forEach((row, height) -> {
                logger.info("第{}行高度: {}", row, height);
            });
        }
    }
    
    @Test
    public void testRowRangeCalculation() {
        logger.info("=== 测试行列范围计算 ===");
        
        AnalysisSheetService service = new AnalysisSheetService();
        
        // 创建行高配置
        JSONObject rowlen = new JSONObject();
        rowlen.put("0", 25);
        rowlen.put("1", 30);
        rowlen.put("2", 60); // 自动换行后增加的高度
        rowlen.put("3", 80); // 自动换行后增加的高度
        rowlen.put("4", 45);
        
        // 测试像素坐标反推行范围
        // 假设图片top=55, height=140（原来是75，现在应该是140）
        try {
            java.lang.reflect.Method method = AnalysisSheetService.class.getDeclaredMethod(
                "calculateRowRangeFromPixels", int.class, int.class, JSONObject.class, int.class);
            method.setAccessible(true);
            
            int[] rowRange = (int[]) method.invoke(service, 55, 140, rowlen, 19);
            logger.info("像素坐标 top=55, height=140 对应的行范围: {}-{}", rowRange[0], rowRange[1]);
            
        } catch (Exception e) {
            logger.error("测试行范围计算失败", e);
        }
        
        logger.info("=== 行列范围计算测试完成 ===");
    }
}
