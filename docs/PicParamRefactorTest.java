package com.logictrue.interfaces.strategy.param;

import com.alibaba.fastjson.JSONObject;
import com.logictrue.interfaces.domain.vo.ReportLenVO;
import com.logictrue.interfaces.service.excel.AnalysisSheetService;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.*;
import java.util.*;
import java.util.List;

/**
 * PicParam重构测试类
 * 验证重构后的图片处理逻辑是否正确
 */
public class PicParamRefactorTest {
    
    private static final Logger logger = LoggerFactory.getLogger(PicParamRefactorTest.class);
    
    @Test
    public void testRefactoredImageProcessing() {
        logger.info("=== 测试重构后的图片处理逻辑 ===");
        
        // 1. 创建测试数据
        JSONObject dbObject = createTestDbObject();
        
        // 2. 模拟单元格渲染阶段 - PicParam记录坐标信息
        simulateCellRendering(dbObject);
        
        // 3. 模拟自动行高计算阶段
        simulateAutoRowHeightCalculation(dbObject);
        
        // 4. 调用图片位置计算
        PicParam.processAllImagePositions(dbObject);
        
        // 5. 验证结果
        verifyImagePositions(dbObject);
        
        logger.info("=== 重构后的图片处理逻辑测试完成 ===");
    }
    
    private JSONObject createTestDbObject() {
        JSONObject dbObject = new JSONObject();
        
        // 设置动态列映射（模拟list数据）
        Map<Integer, Integer> colAddCntMap = new HashMap<>();
        dbObject.put("colAddCntMap", colAddCntMap);
        
        // 设置配置信息
        JSONObject config = new JSONObject();
        
        // 设置原始行高配置（设计时）
        JSONObject rowlen = new JSONObject();
        rowlen.put("0", 25);
        rowlen.put("1", 30);
        rowlen.put("2", 35);  // 这行会在自动计算时变化
        rowlen.put("3", 40);  // 这行会在自动计算时变化
        rowlen.put("4", 45);
        
        // 设置列宽配置
        JSONObject columnlen = new JSONObject();
        columnlen.put("0", 80);
        columnlen.put("1", 90);
        columnlen.put("2", 100);
        columnlen.put("3", 110);
        
        config.put("rowlen", rowlen);
        config.put("columnlen", columnlen);
        dbObject.put("config", config);
        
        return dbObject;
    }
    
    private void simulateCellRendering(JSONObject dbObject) {
        logger.info("--- 模拟单元格渲染阶段 ---");
        
        // 创建PicParam实例
        PicParam picParam = new PicParam(dbObject);
        
        // 模拟图片1：位置在第2-4行，第1-3列
        String pattern1 = "test#2-1-4-3";
        String param1 = "test-image-1.jpg";
        picParam.format(param1, pattern1);
        
        // 模拟图片2：位置在第0-2行，第0-2列
        String pattern2 = "test#0-0-2-2";
        String param2 = "test-image-2.jpg";
        picParam.format(param2, pattern2);
        
        // 验证坐标信息是否正确记录
        JSONObject imageCoordinates = dbObject.getJSONObject("imageCoordinates");
        logger.info("记录的图片坐标信息数量: {}", imageCoordinates != null ? imageCoordinates.size() : 0);
        
        if (imageCoordinates != null) {
            imageCoordinates.forEach((imageName, imageInfoObj) -> {
                JSONObject imageInfo = (JSONObject) imageInfoObj;
                logger.info("图片{}: 行列范围[{}-{}, {}-{}], 图片源: {}", 
                           imageName,
                           imageInfo.getInteger("row1"),
                           imageInfo.getInteger("row2"),
                           imageInfo.getInteger("column1"),
                           imageInfo.getInteger("column2"),
                           imageInfo.getString("src"));
            });
        }
    }
    
    private void simulateAutoRowHeightCalculation(JSONObject dbObject) {
        logger.info("--- 模拟自动行高计算阶段 ---");
        
        // 模拟自动行高计算，第2行和第3行高度增加
        JSONObject config = dbObject.getJSONObject("config");
        JSONObject rowlen = config.getJSONObject("rowlen");
        
        // 模拟自动计算后的行高变化
        rowlen.put("2", 60);  // 从35增加到60
        rowlen.put("3", 80);  // 从40增加到80
        
        logger.info("自动行高计算完成 - 第2行: 35→60, 第3行: 40→80");
    }
    
    private void verifyImagePositions(JSONObject dbObject) {
        logger.info("--- 验证图片位置计算结果 ---");
        
        JSONObject images = dbObject.getJSONObject("images");
        if (images == null || images.isEmpty()) {
            logger.error("❌ 没有找到图片配置");
            return;
        }
        
        logger.info("生成的图片配置数量: {}", images.size());
        
        images.forEach((imageName, imageObj) -> {
            JSONObject image = (JSONObject) imageObj;
            JSONObject defaultConfig = image.getJSONObject("default");
            
            if (defaultConfig != null) {
                int left = defaultConfig.getInteger("left");
                int top = defaultConfig.getInteger("top");
                int width = defaultConfig.getInteger("width");
                int height = defaultConfig.getInteger("height");
                String src = image.getString("src");
                
                logger.info("图片{}: 位置[left={}, top={}, width={}, height={}], 源: {}", 
                           imageName, left, top, width, height, src);
                
                // 验证特定图片的位置计算
                if ("test-image-1.jpg".equals(src)) {
                    // 图片1位置：第2-4行，第1-3列
                    // 期望：left=80 (第0列), top=55 (第0+1行), width=190 (第1+2列), height=140 (第2+3行，60+80)
                    assert left == 80 : "图片1左边距应该是80，实际是" + left;
                    assert top == 55 : "图片1顶部距离应该是55，实际是" + top;
                    assert width == 190 : "图片1宽度应该是190，实际是" + width;
                    assert height == 140 : "图片1高度应该是140，实际是" + height;
                    logger.info("✅ 图片1位置计算正确");
                } else if ("test-image-2.jpg".equals(src)) {
                    // 图片2位置：第0-2行，第0-2列
                    // 期望：left=0, top=0, width=170 (第0+1列，80+90), height=55 (第0+1行，25+30)
                    assert left == 0 : "图片2左边距应该是0，实际是" + left;
                    assert top == 0 : "图片2顶部距离应该是0，实际是" + top;
                    assert width == 170 : "图片2宽度应该是170，实际是" + width;
                    assert height == 55 : "图片2高度应该是55，实际是" + height;
                    logger.info("✅ 图片2位置计算正确");
                }
            }
        });
        
        // 验证临时坐标信息已被清理
        JSONObject imageCoordinates = dbObject.getJSONObject("imageCoordinates");
        if (imageCoordinates == null) {
            logger.info("✅ 临时坐标信息已正确清理");
        } else {
            logger.warn("⚠️ 临时坐标信息未被清理");
        }
    }
    
    @Test
    public void testImagePositionCalculationMethod() {
        logger.info("=== 测试图片位置计算方法 ===");
        
        // 创建测试行高列宽配置
        JSONObject rowlen = new JSONObject();
        rowlen.put("0", 25);
        rowlen.put("1", 30);
        rowlen.put("2", 60);  // 自动计算后的高度
        rowlen.put("3", 80);  // 自动计算后的高度
        
        JSONObject columnlen = new JSONObject();
        columnlen.put("0", 80);
        columnlen.put("1", 90);
        columnlen.put("2", 100);
        
        // 测试位置计算
        int[] position = PicParam.calculateImagePixelPosition(2, 1, 4, 3, rowlen, columnlen);
        
        int left = position[0];
        int top = position[1];
        int width = position[2];
        int height = position[3];
        
        logger.info("计算结果 - left: {}, top: {}, width: {}, height: {}", left, top, width, height);
        
        // 验证计算结果
        assert left == 80 : "左边距计算错误，期望80，实际" + left;
        assert top == 55 : "顶部距离计算错误，期望55，实际" + top;
        assert width == 190 : "宽度计算错误，期望190，实际" + width;
        assert height == 140 : "高度计算错误，期望140，实际" + height;
        
        logger.info("✅ 图片位置计算方法测试通过");
    }
    
    @Test
    public void testWithDynamicRows() {
        logger.info("=== 测试动态行数情况 ===");
        
        JSONObject dbObject = createTestDbObject();
        
        // 设置动态行数
        Map<Integer, Integer> colAddCntMap = new HashMap<>();
        colAddCntMap.put(1, 2); // 第1列有2行动态数据
        colAddCntMap.put(2, 2); // 第2列有2行动态数据
        dbObject.put("colAddCntMap", colAddCntMap);
        
        // 创建PicParam并处理图片
        PicParam picParam = new PicParam(dbObject);
        
        // 图片位置：第2-4行，第1-3列，应该受到动态行数影响
        String pattern = "test#2-1-4-3";
        String param = "dynamic-test-image.jpg";
        picParam.format(param, pattern);
        
        // 验证动态调整后的坐标
        JSONObject imageCoordinates = dbObject.getJSONObject("imageCoordinates");
        if (imageCoordinates != null) {
            imageCoordinates.forEach((imageName, imageInfoObj) -> {
                JSONObject imageInfo = (JSONObject) imageInfoObj;
                int row1 = imageInfo.getInteger("row1");
                int row2 = imageInfo.getInteger("row2");
                
                logger.info("动态调整后的图片行范围: {}-{}", row1, row2);
                
                // 由于第1、2列都有2行动态数据，图片应该向下偏移2行
                assert row1 == 4 : "动态调整后起始行应该是4，实际是" + row1;
                assert row2 == 6 : "动态调整后结束行应该是6，实际是" + row2;
            });
        }
        
        logger.info("✅ 动态行数测试通过");
    }
}
