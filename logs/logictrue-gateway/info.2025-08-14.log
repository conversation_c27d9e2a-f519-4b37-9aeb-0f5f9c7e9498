19:26:19.657 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.0.Final
19:26:21.121 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:/home/<USER>/nacos/config
19:26:21.124 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of 5f82406b-86b2-426d-9263-292ccefb9989_config-0
19:26:21.169 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 
19:26:21.196 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
19:26:21.209 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 3 keys and 10 values 
19:26:21.367 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 154 ms to scan 247 urls, producing 0 keys and 0 values 
19:26:21.378 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
19:26:21.391 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
19:26:21.406 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
19:26:21.555 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 146 ms to scan 247 urls, producing 0 keys and 0 values 
19:26:21.556 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f82406b-86b2-426d-9263-292ccefb9989_config-0]RpcClient init label, labels={module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:26:21.557 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f82406b-86b2-426d-9263-292ccefb9989_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/727860268
19:26:21.557 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f82406b-86b2-426d-9263-292ccefb9989_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$333/1717739363
19:26:21.558 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f82406b-86b2-426d-9263-292ccefb9989_config-0]Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:26:21.558 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f82406b-86b2-426d-9263-292ccefb9989_config-0]RpcClient init, ServerListFactory =com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:26:21.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f82406b-86b2-426d-9263-292ccefb9989_config-0] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
19:26:22.199 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f82406b-86b2-426d-9263-292ccefb9989_config-0] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755170781969_172.21.0.1_45828
19:26:22.200 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f82406b-86b2-426d-9263-292ccefb9989_config-0]Notify connected event to listeners.
19:26:22.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f82406b-86b2-426d-9263-292ccefb9989_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:26:22.200 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,681] - [5f82406b-86b2-426d-9263-292ccefb9989_config-0] Connected,notify listen context...
19:26:22.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f82406b-86b2-426d-9263-292ccefb9989_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
19:26:22.235 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,56] - limitTime:5.0
19:26:22.254 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
19:26:22.326 [main] INFO  c.l.g.LogicTrueGatewayApplication - [logStartupProfileInfo,663] - The following profiles are active: dev
19:26:27.358 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.17.5
19:26:27.664 [redisson-netty-5-5] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
19:26:27.734 [redisson-netty-5-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
19:26:29.047 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
19:26:29.214 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,63] - initializer namespace from System Property : null
19:26:29.214 [main] INFO  c.a.n.client.naming - [call,69] - initializer namespace from System Environment :null
19:26:29.215 [main] INFO  c.a.n.client.naming - [call,79] - initializer namespace from System Property :null
19:26:29.229 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of 742953b2-0743-4894-90f0-a56edd415ea2
19:26:29.230 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]RpcClient init label, labels={module=naming, source=sdk}
19:26:29.231 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]RpcClient init, ServerListFactory =com.alibaba.nacos.client.naming.core.ServerListManager
19:26:29.232 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:26:29.233 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:26:29.233 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
19:26:29.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755170789241_172.21.0.1_40990
19:26:29.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:26:29.349 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]Notify connected event to listeners.
19:26:29.349 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
19:26:29.349 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
19:26:29.623 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
19:26:29.948 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] sentinel-logictrue-gateway+DEFAULT_GROUP
19:26:29.960 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=, dataId=sentinel-logictrue-gateway, group=DEFAULT_GROUP, cnt=1
19:26:29.960 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of 0d468e06-14a3-40a8-983f-c330ace88426_config-0
19:26:29.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d468e06-14a3-40a8-983f-c330ace88426_config-0]RpcClient init label, labels={module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:26:29.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d468e06-14a3-40a8-983f-c330ace88426_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/727860268
19:26:29.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d468e06-14a3-40a8-983f-c330ace88426_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$333/1717739363
19:26:29.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d468e06-14a3-40a8-983f-c330ace88426_config-0]Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:26:29.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d468e06-14a3-40a8-983f-c330ace88426_config-0]RpcClient init, ServerListFactory =com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:26:29.967 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d468e06-14a3-40a8-983f-c330ace88426_config-0] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
19:26:30.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d468e06-14a3-40a8-983f-c330ace88426_config-0] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755170789983_172.21.0.1_41000
19:26:30.108 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d468e06-14a3-40a8-983f-c330ace88426_config-0]Notify connected event to listeners.
19:26:30.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d468e06-14a3-40a8-983f-c330ace88426_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:26:30.109 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,681] - [0d468e06-14a3-40a8-983f-c330ace88426_config-0] Connected,notify listen context...
19:26:30.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d468e06-14a3-40a8-983f-c330ace88426_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
19:26:30.218 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@logictrue-gateway@@DEFAULT -> []
19:26:30.221 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-gateway@@DEFAULT -> []
19:26:30.326 [boundedElastic-4] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(1) service: DEFAULT_GROUP@@logictrue-auth -> [{"instanceId":"**************#9211#DEFAULT#DEFAULT_GROUP@@logictrue-auth","ip":"**************","port":9211,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-auth","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
19:26:30.327 [boundedElastic-4] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-auth -> [{"instanceId":"**************#9211#DEFAULT#DEFAULT_GROUP@@logictrue-auth","ip":"**************","port":9211,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-auth","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
19:26:30.753 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]receive server push request,request=NotifySubscriberRequest,requestId=25
19:26:30.753 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]ack server push request,request=NotifySubscriberRequest,requestId=25
19:26:30.843 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]receive server push request,request=NotifySubscriberRequest,requestId=26
19:26:30.844 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]ack server push request,request=NotifySubscriberRequest,requestId=26
19:26:32.266 [main] INFO  c.a.n.client.naming - [registerService,112] - [REGISTER-SERVICE] dev registering service logictrue-gateway with instance Instance{instanceId='null', ip='**************', port=8080, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
19:26:32.282 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP logictrue-gateway **************:8080 register finished
19:26:32.310 [boundedElastic-1] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(1) service: DEFAULT_GROUP@@logictrue-gateway -> [{"instanceId":"**************#8080#DEFAULT#DEFAULT_GROUP@@logictrue-gateway","ip":"**************","port":8080,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
19:26:32.311 [boundedElastic-1] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-gateway -> [{"instanceId":"**************#8080#DEFAULT#DEFAULT_GROUP@@logictrue-gateway","ip":"**************","port":8080,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
19:26:32.345 [main] INFO  c.l.g.LogicTrueGatewayApplication - [logStarted,61] - Started LogicTrueGatewayApplication in 14.229 seconds (JVM running for 14.879)
19:26:32.356 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-gateway+DEFAULT_GROUP+dev
19:26:32.356 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-gateway, group=DEFAULT_GROUP, cnt=1
19:26:32.357 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-gateway-dev.yml+DEFAULT_GROUP+dev
19:26:32.357 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-gateway-dev.yml, group=DEFAULT_GROUP, cnt=1
19:26:32.357 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-gateway.yml+DEFAULT_GROUP+dev
19:26:32.357 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-gateway.yml, group=DEFAULT_GROUP, cnt=1
19:26:32.855 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]receive server push request,request=NotifySubscriberRequest,requestId=27
19:26:32.856 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]ack server push request,request=NotifySubscriberRequest,requestId=27
19:26:33.242 [com.alibaba.nacos.client.naming.updater.0] INFO  c.a.n.client.naming - [isChangedServiceInfo,235] - new ips(1) service: DEFAULT_GROUP@@logictrue-gateway@@DEFAULT -> [{"instanceId":"**************#8080#DEFAULT#DEFAULT_GROUP@@logictrue-gateway","ip":"**************","port":8080,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
19:26:33.243 [com.alibaba.nacos.client.naming.updater.0] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-gateway@@DEFAULT -> [{"instanceId":"**************#8080#DEFAULT#DEFAULT_GROUP@@logictrue-gateway","ip":"**************","port":8080,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
19:27:30.434 [boundedElastic-2] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(1) service: DEFAULT_GROUP@@logictrue-system -> [{"instanceId":"**************#9201#DEFAULT#DEFAULT_GROUP@@logictrue-system","ip":"**************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
19:27:30.434 [boundedElastic-2] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-system -> [{"instanceId":"**************#9201#DEFAULT#DEFAULT_GROUP@@logictrue-system","ip":"**************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
19:27:31.029 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]receive server push request,request=NotifySubscriberRequest,requestId=29
19:27:31.029 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]ack server push request,request=NotifySubscriberRequest,requestId=29
19:28:00.429 [boundedElastic-1] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
19:28:00.429 [boundedElastic-1] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
19:28:00.991 [nacos-grpc-client-executor-75] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]receive server push request,request=NotifySubscriberRequest,requestId=32
19:28:00.992 [nacos-grpc-client-executor-75] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]ack server push request,request=NotifySubscriberRequest,requestId=32
19:45:25.665 [boundedElastic-1] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@logictrue-file -> []
19:45:25.666 [boundedElastic-1] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-file -> []
19:45:26.225 [nacos-grpc-client-executor-444] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]receive server push request,request=NotifySubscriberRequest,requestId=33
19:45:26.226 [nacos-grpc-client-executor-444] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]ack server push request,request=NotifySubscriberRequest,requestId=33
19:45:33.292 [reactor-http-epoll-9] INFO  c.l.g.f.XssFilter - [filter,68] - Xss过滤器执行成功 地址：http://localhost/auth/login 消耗时间：0
19:45:33.979 [boundedElastic-2] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@logictrue-llm -> []
19:45:33.979 [boundedElastic-2] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-llm -> []
19:45:34.561 [nacos-grpc-client-executor-461] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]receive server push request,request=NotifySubscriberRequest,requestId=35
19:45:34.561 [nacos-grpc-client-executor-461] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]ack server push request,request=NotifySubscriberRequest,requestId=35
22:51:29.938 [nacos-grpc-client-executor-335] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d468e06-14a3-40a8-983f-c330ace88426_config-0]receive server push request,request=ClientDetectionRequest,requestId=42
22:51:29.938 [nacos-grpc-client-executor-556] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]receive server push request,request=ClientDetectionRequest,requestId=44
22:51:29.939 [nacos-grpc-client-executor-556] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [742953b2-0743-4894-90f0-a56edd415ea2]ack server push request,request=ClientDetectionRequest,requestId=44
22:51:29.939 [nacos-grpc-client-executor-335] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d468e06-14a3-40a8-983f-c330ace88426_config-0]ack server push request,request=ClientDetectionRequest,requestId=42
22:51:29.940 [nacos-grpc-client-executor-356] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f82406b-86b2-426d-9263-292ccefb9989_config-0]receive server push request,request=ClientDetectionRequest,requestId=43
22:51:29.940 [nacos-grpc-client-executor-356] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f82406b-86b2-426d-9263-292ccefb9989_config-0]ack server push request,request=ClientDetectionRequest,requestId=43
