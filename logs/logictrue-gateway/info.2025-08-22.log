23:45:29.366 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.0.Final
23:45:29.848 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:/home/<USER>/nacos/config
23:45:29.854 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of 46502396-1920-4db5-b6f0-2b64c9c35cf7_config-0
23:45:29.940 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 48 ms to scan 1 urls, producing 3 keys and 6 values 
23:45:29.976 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 4 keys and 9 values 
23:45:29.990 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 3 keys and 10 values 
23:45:30.154 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 161 ms to scan 247 urls, producing 0 keys and 0 values 
23:45:30.164 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
23:45:30.179 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
23:45:30.194 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
23:45:30.327 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 130 ms to scan 247 urls, producing 0 keys and 0 values 
23:45:30.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46502396-1920-4db5-b6f0-2b64c9c35cf7_config-0]RpcClient init label, labels={module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
23:45:30.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46502396-1920-4db5-b6f0-2b64c9c35cf7_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/665137804
23:45:30.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46502396-1920-4db5-b6f0-2b64c9c35cf7_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$333/509559152
23:45:30.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46502396-1920-4db5-b6f0-2b64c9c35cf7_config-0]Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
23:45:30.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46502396-1920-4db5-b6f0-2b64c9c35cf7_config-0]RpcClient init, ServerListFactory =com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
23:45:30.338 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46502396-1920-4db5-b6f0-2b64c9c35cf7_config-0] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
23:45:31.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46502396-1920-4db5-b6f0-2b64c9c35cf7_config-0] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755877530865_172.21.0.1_35152
23:45:31.228 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46502396-1920-4db5-b6f0-2b64c9c35cf7_config-0]Notify connected event to listeners.
23:45:31.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46502396-1920-4db5-b6f0-2b64c9c35cf7_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
23:45:31.228 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,681] - [46502396-1920-4db5-b6f0-2b64c9c35cf7_config-0] Connected,notify listen context...
23:45:31.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46502396-1920-4db5-b6f0-2b64c9c35cf7_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
23:45:31.338 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,56] - limitTime:5.0
23:45:31.482 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
23:45:31.587 [main] INFO  c.l.g.LogicTrueGatewayApplication - [logStartupProfileInfo,663] - The following profiles are active: dev
23:45:35.651 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.17.5
23:45:35.913 [redisson-netty-5-5] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
23:45:36.018 [redisson-netty-5-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
23:45:36.368 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
23:45:36.619 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,63] - initializer namespace from System Property : null
23:45:36.620 [main] INFO  c.a.n.client.naming - [call,69] - initializer namespace from System Environment :null
23:45:36.621 [main] INFO  c.a.n.client.naming - [call,79] - initializer namespace from System Property :null
23:45:36.643 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of a37a5bf9-9fd3-449c-9510-5a69e0b34093
23:45:36.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]RpcClient init label, labels={module=naming, source=sdk}
23:45:36.646 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]RpcClient init, ServerListFactory =com.alibaba.nacos.client.naming.core.ServerListManager
23:45:36.647 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
23:45:36.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
23:45:36.649 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
23:45:36.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755877536660_172.21.0.1_58412
23:45:36.770 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
23:45:36.770 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]Notify connected event to listeners.
23:45:36.770 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
23:45:36.770 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
23:45:37.028 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
23:45:37.476 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] sentinel-logictrue-gateway+DEFAULT_GROUP
23:45:37.486 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=, dataId=sentinel-logictrue-gateway, group=DEFAULT_GROUP, cnt=1
23:45:37.487 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of f92e5218-4035-4220-a8b5-e8d7c88a7c91_config-0
23:45:37.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f92e5218-4035-4220-a8b5-e8d7c88a7c91_config-0]RpcClient init label, labels={module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
23:45:37.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f92e5218-4035-4220-a8b5-e8d7c88a7c91_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/665137804
23:45:37.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f92e5218-4035-4220-a8b5-e8d7c88a7c91_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$333/509559152
23:45:37.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f92e5218-4035-4220-a8b5-e8d7c88a7c91_config-0]Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
23:45:37.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f92e5218-4035-4220-a8b5-e8d7c88a7c91_config-0]RpcClient init, ServerListFactory =com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
23:45:37.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f92e5218-4035-4220-a8b5-e8d7c88a7c91_config-0] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
23:45:37.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f92e5218-4035-4220-a8b5-e8d7c88a7c91_config-0] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755877537499_172.21.0.1_58430
23:45:37.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f92e5218-4035-4220-a8b5-e8d7c88a7c91_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
23:45:37.609 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f92e5218-4035-4220-a8b5-e8d7c88a7c91_config-0]Notify connected event to listeners.
23:45:37.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f92e5218-4035-4220-a8b5-e8d7c88a7c91_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
23:45:37.610 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,681] - [f92e5218-4035-4220-a8b5-e8d7c88a7c91_config-0] Connected,notify listen context...
23:45:37.748 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@logictrue-gateway@@DEFAULT -> []
23:45:37.752 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-gateway@@DEFAULT -> []
23:45:37.822 [main] INFO  c.a.n.client.naming - [registerService,112] - [REGISTER-SERVICE] dev registering service logictrue-gateway with instance Instance{instanceId='null', ip='**************', port=8080, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
23:45:37.899 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP logictrue-gateway **************:8080 register finished
23:45:38.016 [main] INFO  c.l.g.LogicTrueGatewayApplication - [logStarted,61] - Started LogicTrueGatewayApplication in 9.184 seconds (JVM running for 9.824)
23:45:38.033 [boundedElastic-6] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(1) service: DEFAULT_GROUP@@logictrue-gateway -> [{"instanceId":"**************#8080#DEFAULT#DEFAULT_GROUP@@logictrue-gateway","ip":"**************","port":8080,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
23:45:38.034 [boundedElastic-6] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-gateway -> [{"instanceId":"**************#8080#DEFAULT#DEFAULT_GROUP@@logictrue-gateway","ip":"**************","port":8080,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
23:45:38.040 [boundedElastic-5] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(1) service: DEFAULT_GROUP@@logictrue-gateway -> [{"instanceId":"**************#8080#DEFAULT#DEFAULT_GROUP@@logictrue-gateway","ip":"**************","port":8080,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
23:45:38.040 [boundedElastic-5] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-gateway -> [{"instanceId":"**************#8080#DEFAULT#DEFAULT_GROUP@@logictrue-gateway","ip":"**************","port":8080,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
23:45:38.041 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-gateway+DEFAULT_GROUP+dev
23:45:38.041 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-gateway, group=DEFAULT_GROUP, cnt=1
23:45:38.047 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-gateway-dev.yml+DEFAULT_GROUP+dev
23:45:38.047 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-gateway-dev.yml, group=DEFAULT_GROUP, cnt=1
23:45:38.048 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-gateway.yml+DEFAULT_GROUP+dev
23:45:38.048 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-gateway.yml, group=DEFAULT_GROUP, cnt=1
23:45:38.304 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=1
23:45:38.306 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=1
23:45:38.716 [com.alibaba.nacos.client.naming.updater.0] INFO  c.a.n.client.naming - [isChangedServiceInfo,235] - new ips(1) service: DEFAULT_GROUP@@logictrue-gateway@@DEFAULT -> [{"instanceId":"**************#8080#DEFAULT#DEFAULT_GROUP@@logictrue-gateway","ip":"**************","port":8080,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
23:45:38.716 [com.alibaba.nacos.client.naming.updater.0] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-gateway@@DEFAULT -> [{"instanceId":"**************#8080#DEFAULT#DEFAULT_GROUP@@logictrue-gateway","ip":"**************","port":8080,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
23:46:09.024 [boundedElastic-5] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(1) service: DEFAULT_GROUP@@logictrue-auth -> [{"instanceId":"**************#9211#DEFAULT#DEFAULT_GROUP@@logictrue-auth","ip":"**************","port":9211,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-auth","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
23:46:09.024 [boundedElastic-1] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(1) service: DEFAULT_GROUP@@logictrue-system -> [{"instanceId":"**************#9201#DEFAULT#DEFAULT_GROUP@@logictrue-system","ip":"**************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
23:46:09.026 [boundedElastic-5] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-auth -> [{"instanceId":"**************#9211#DEFAULT#DEFAULT_GROUP@@logictrue-auth","ip":"**************","port":9211,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-auth","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
23:46:09.026 [boundedElastic-1] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-system -> [{"instanceId":"**************#9201#DEFAULT#DEFAULT_GROUP@@logictrue-system","ip":"**************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
23:46:09.598 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=4
23:46:09.598 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=4
23:46:09.600 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=5
23:46:09.600 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=5
23:46:39.025 [boundedElastic-6] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
23:46:39.025 [boundedElastic-6] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
23:46:39.530 [nacos-grpc-client-executor-63] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=7
23:46:39.530 [nacos-grpc-client-executor-63] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=7
23:47:02.123 [boundedElastic-4] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@logictrue-file -> []
23:47:02.124 [boundedElastic-4] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-file -> []
23:47:02.648 [nacos-grpc-client-executor-82] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=8
23:47:02.649 [nacos-grpc-client-executor-82] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=8
23:47:05.843 [reactor-http-epoll-9] INFO  c.l.g.f.XssFilter - [filter,68] - Xss过滤器执行成功 地址：http://127.0.0.1/auth/login 消耗时间：0
23:47:06.200 [boundedElastic-3] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@logictrue-llm -> []
23:47:06.201 [boundedElastic-3] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-llm -> []
23:47:06.756 [nacos-grpc-client-executor-90] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=10
23:47:06.756 [nacos-grpc-client-executor-90] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=10
