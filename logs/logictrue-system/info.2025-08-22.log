23:45:30.732 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.0.Final
23:45:31.231 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:/home/<USER>/nacos/config
23:45:31.233 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of 7abfacbd-f62b-42b4-b637-96404c324562_config-0
23:45:31.295 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 46 ms to scan 1 urls, producing 3 keys and 6 values 
23:45:31.343 [main] INFO  o.r.Reflections - [scan,232] - <PERSON>flections took 34 ms to scan 1 urls, producing 4 keys and 9 values 
23:45:31.357 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 3 keys and 10 values 
23:45:31.582 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 221 ms to scan 308 urls, producing 0 keys and 0 values 
23:45:31.594 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
23:45:31.606 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
23:45:31.617 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
23:45:31.817 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 197 ms to scan 308 urls, producing 0 keys and 0 values 
23:45:31.819 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7abfacbd-f62b-42b4-b637-96404c324562_config-0]RpcClient init label, labels={module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
23:45:31.820 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7abfacbd-f62b-42b4-b637-96404c324562_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$331/1873121466
23:45:31.821 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7abfacbd-f62b-42b4-b637-96404c324562_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/115945887
23:45:31.822 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7abfacbd-f62b-42b4-b637-96404c324562_config-0]Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
23:45:31.823 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7abfacbd-f62b-42b4-b637-96404c324562_config-0]RpcClient init, ServerListFactory =com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
23:45:31.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7abfacbd-f62b-42b4-b637-96404c324562_config-0] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
23:45:32.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7abfacbd-f62b-42b4-b637-96404c324562_config-0] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755877532218_172.21.0.1_35166
23:45:32.461 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7abfacbd-f62b-42b4-b637-96404c324562_config-0]Notify connected event to listeners.
23:45:32.461 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,681] - [7abfacbd-f62b-42b4-b637-96404c324562_config-0] Connected,notify listen context...
23:45:32.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7abfacbd-f62b-42b4-b637-96404c324562_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
23:45:32.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7abfacbd-f62b-42b4-b637-96404c324562_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
23:45:32.492 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,56] - limitTime:5.0
23:45:32.513 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
23:45:32.650 [main] INFO  c.l.s.LogicTrueSystemApplication - [logStartupProfileInfo,663] - The following profiles are active: dev
23:45:37.167 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9201"]
23:45:37.167 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:45:37.168 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:45:37.270 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:45:38.213 [main] INFO  o.m.driver.cluster - [info,71] - Cluster created with settings {hosts=[localhost:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
23:45:38.302 [cluster-ClusterId{value='68a890a2da8fa3643e95e8eb', description='null'}-localhost:27017] INFO  o.m.d.connection - [info,71] - Opened connection [connectionId{localValue:1, serverValue:48192}] to localhost:27017
23:45:38.302 [cluster-rtt-ClusterId{value='68a890a2da8fa3643e95e8eb', description='null'}-localhost:27017] INFO  o.m.d.connection - [info,71] - Opened connection [connectionId{localValue:2, serverValue:48191}] to localhost:27017
23:45:38.303 [cluster-ClusterId{value='68a890a2da8fa3643e95e8eb', description='null'}-localhost:27017] INFO  o.m.driver.cluster - [info,71] - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=25, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=27021459}
23:45:39.420 [main] INFO  o.m.d.connection - [info,71] - Opened connection [connectionId{localValue:3, serverValue:48193}] to localhost:27017
23:45:39.961 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,63] - initializer namespace from System Property : null
23:45:39.962 [main] INFO  c.a.n.client.naming - [call,69] - initializer namespace from System Environment :null
23:45:39.962 [main] INFO  c.a.n.client.naming - [call,79] - initializer namespace from System Property :null
23:45:39.975 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of 1079cfd9-444c-4c72-befd-46d3733fa81b
23:45:39.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1079cfd9-444c-4c72-befd-46d3733fa81b]RpcClient init label, labels={module=naming, source=sdk}
23:45:39.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1079cfd9-444c-4c72-befd-46d3733fa81b]RpcClient init, ServerListFactory =com.alibaba.nacos.client.naming.core.ServerListManager
23:45:39.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1079cfd9-444c-4c72-befd-46d3733fa81b]Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
23:45:39.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1079cfd9-444c-4c72-befd-46d3733fa81b]Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
23:45:39.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1079cfd9-444c-4c72-befd-46d3733fa81b] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
23:45:40.091 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1079cfd9-444c-4c72-befd-46d3733fa81b] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755877539986_172.21.0.1_58446
23:45:40.092 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1079cfd9-444c-4c72-befd-46d3733fa81b]Notify connected event to listeners.
23:45:40.092 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1079cfd9-444c-4c72-befd-46d3733fa81b]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
23:45:40.092 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
23:45:40.092 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1079cfd9-444c-4c72-befd-46d3733fa81b]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
23:45:40.346 [main] INFO  c.a.d.p.DruidDataSource - [init,995] - {dataSource-1} inited
23:45:42.568 [main] INFO  c.a.d.p.DruidDataSource - [init,995] - {dataSource-2,ldf_server} inited
23:45:43.354 [main] INFO  c.g.d.c.DozerBeanMapperBuilder - [build,544] - Initializing Dozer. Version: 6.4.0, Thread Name: main
23:45:43.356 [main] INFO  c.g.d.c.u.RuntimeUtils - [isOSGi,53] - OSGi support is false
23:45:43.362 [main] INFO  c.g.d.c.c.r.LegacyPropertiesSettingsResolver - [processFile,60] - Trying to find Dozer configuration file: dozer.properties
23:45:43.363 [main] INFO  c.g.d.c.c.r.LegacyPropertiesSettingsResolver - [processFile,63] - Failed to find dozer.properties via com.github.dozermapper.core.config.resolvers.LegacyPropertiesSettingsResolver.
23:45:44.315 [main] INFO  c.a.d.p.DruidDataSource - [init,995] - {dataSource-3,ldf_server} inited
23:45:44.847 [main] INFO  c.a.d.p.DruidDataSource - [init,995] - {dataSource-4,ldf_server_zyrm} inited
23:45:45.289 [main] INFO  c.a.d.p.DruidDataSource - [init,995] - {dataSource-5,ldf_server_zyrm} inited
23:45:48.366 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of 0293a216-1b9d-4815-8b82-30982da930cd_config-0
23:45:48.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0293a216-1b9d-4815-8b82-30982da930cd_config-0]RpcClient init label, labels={module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
23:45:48.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0293a216-1b9d-4815-8b82-30982da930cd_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$331/1873121466
23:45:48.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0293a216-1b9d-4815-8b82-30982da930cd_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/115945887
23:45:48.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0293a216-1b9d-4815-8b82-30982da930cd_config-0]Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
23:45:48.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0293a216-1b9d-4815-8b82-30982da930cd_config-0]RpcClient init, ServerListFactory =com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
23:45:48.368 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0293a216-1b9d-4815-8b82-30982da930cd_config-0] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
23:45:48.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0293a216-1b9d-4815-8b82-30982da930cd_config-0] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755877548406_172.21.0.1_56124
23:45:48.512 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0293a216-1b9d-4815-8b82-30982da930cd_config-0]Notify connected event to listeners.
23:45:48.512 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0293a216-1b9d-4815-8b82-30982da930cd_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
23:45:48.512 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,681] - [0293a216-1b9d-4815-8b82-30982da930cd_config-0] Connected,notify listen context...
23:45:48.512 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0293a216-1b9d-4815-8b82-30982da930cd_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
23:45:48.646 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.17.5
23:45:48.713 [redisson-netty-5-5] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
23:45:48.747 [redisson-netty-5-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
23:45:49.353 [main] INFO  c.a.c.s.SentinelWebAutoConfiguration - [addInterceptors,80] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
23:45:51.165 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@logictrue-system@@DEFAULT -> []
23:45:51.172 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-system@@DEFAULT -> []
23:45:51.179 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9201"]
23:45:51.194 [main] INFO  c.a.n.client.naming - [registerService,112] - [REGISTER-SERVICE] dev registering service logictrue-system with instance Instance{instanceId='null', ip='**************', port=9201, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
23:45:51.201 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP logictrue-system **************:9201 register finished
23:45:51.213 [main] INFO  c.l.s.c.TenantDataSourceRefreshConfig - [start,35] - TenantDataSource数据源同步监听启动
23:45:51.675 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1079cfd9-444c-4c72-befd-46d3733fa81b]receive server push request,request=NotifySubscriberRequest,requestId=3
23:45:51.680 [nacos-grpc-client-executor-12] INFO  c.a.n.client.naming - [isChangedServiceInfo,235] - new ips(1) service: DEFAULT_GROUP@@logictrue-system@@DEFAULT -> [{"instanceId":"**************#9201#DEFAULT#DEFAULT_GROUP@@logictrue-system","ip":"**************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
23:45:51.680 [nacos-grpc-client-executor-12] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-system@@DEFAULT -> [{"instanceId":"**************#9201#DEFAULT#DEFAULT_GROUP@@logictrue-system","ip":"**************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
23:45:51.680 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1079cfd9-444c-4c72-befd-46d3733fa81b]ack server push request,request=NotifySubscriberRequest,requestId=3
23:45:52.355 [main] INFO  c.l.s.t.ApiLogTask - [lambda$configureTasks$1,33] - 定时删除日志记录cron表达式：0 0 0 ? * *
23:45:52.363 [main] INFO  c.l.s.LogicTrueSystemApplication - [logStarted,61] - Started LogicTrueSystemApplication in 22.149 seconds (JVM running for 22.707)
23:45:52.373 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-system-dev.yml+DEFAULT_GROUP+dev
23:45:52.374 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-system-dev.yml, group=DEFAULT_GROUP, cnt=1
23:45:52.375 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-system+DEFAULT_GROUP+dev
23:45:52.375 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-system, group=DEFAULT_GROUP, cnt=1
23:45:52.376 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-system.yml+DEFAULT_GROUP+dev
23:45:52.376 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-system.yml, group=DEFAULT_GROUP, cnt=1
23:45:52.850 [RMI TCP Connection(4)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:47:06.113 [asyncExecutor-1] INFO  o.a.d.i.File2Stream - [info,106] - path to stream library/ambiguity.dic
23:47:06.115 [asyncExecutor-1] INFO  o.a.d.i.File2Stream - [info,106] - path to stream library/default.dic
23:47:07.034 [asyncExecutor-1] INFO  o.a.l.DATDictionary - [info,106] - init core library ok use time : 848
23:47:07.304 [asyncExecutor-1] INFO  o.a.l.NgramLibrary - [info,106] - init ngram ok use time :267
