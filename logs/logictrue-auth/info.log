23:45:32.350 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.0.Final
23:45:32.823 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:/home/<USER>/nacos/config
23:45:32.827 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of b5edde58-9413-4575-abb2-f67f1ab8b71a_config-0
23:45:32.903 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 33 ms to scan 1 urls, producing 3 keys and 6 values 
23:45:32.944 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 4 keys and 9 values 
23:45:32.958 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 3 keys and 10 values 
23:45:33.159 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 196 ms to scan 228 urls, producing 0 keys and 0 values 
23:45:33.171 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
23:45:33.194 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 1 keys and 7 values 
23:45:33.208 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
23:45:33.352 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 142 ms to scan 228 urls, producing 0 keys and 0 values 
23:45:33.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5edde58-9413-4575-abb2-f67f1ab8b71a_config-0]RpcClient init label, labels={module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
23:45:33.355 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5edde58-9413-4575-abb2-f67f1ab8b71a_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$331/1964847681
23:45:33.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5edde58-9413-4575-abb2-f67f1ab8b71a_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/1992801971
23:45:33.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5edde58-9413-4575-abb2-f67f1ab8b71a_config-0]Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
23:45:33.357 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5edde58-9413-4575-abb2-f67f1ab8b71a_config-0]RpcClient init, ServerListFactory =com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
23:45:33.365 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5edde58-9413-4575-abb2-f67f1ab8b71a_config-0] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
23:45:34.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5edde58-9413-4575-abb2-f67f1ab8b71a_config-0] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755877533737_172.21.0.1_35180
23:45:34.006 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5edde58-9413-4575-abb2-f67f1ab8b71a_config-0]Notify connected event to listeners.
23:45:34.007 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5edde58-9413-4575-abb2-f67f1ab8b71a_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
23:45:34.007 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,681] - [b5edde58-9413-4575-abb2-f67f1ab8b71a_config-0] Connected,notify listen context...
23:45:34.007 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5edde58-9413-4575-abb2-f67f1ab8b71a_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
23:45:34.046 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,56] - limitTime:5.0
23:45:34.080 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
23:45:34.175 [main] INFO  c.l.a.LogicTrueAuthApplication - [logStartupProfileInfo,663] - The following profiles are active: dev
23:45:36.137 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9211"]
23:45:36.137 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:45:36.138 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:45:36.220 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:45:37.858 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.17.5
23:45:38.042 [redisson-netty-5-7] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
23:45:38.171 [redisson-netty-5-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
23:45:38.579 [main] INFO  c.a.c.s.SentinelWebAutoConfiguration - [addInterceptors,80] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
23:45:38.819 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,63] - initializer namespace from System Property : null
23:45:38.820 [main] INFO  c.a.n.client.naming - [call,69] - initializer namespace from System Environment :null
23:45:38.821 [main] INFO  c.a.n.client.naming - [call,79] - initializer namespace from System Property :null
23:45:38.838 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of 79f562a5-9184-4a63-b2d7-e66a4be9275e
23:45:38.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79f562a5-9184-4a63-b2d7-e66a4be9275e]RpcClient init label, labels={module=naming, source=sdk}
23:45:38.840 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79f562a5-9184-4a63-b2d7-e66a4be9275e]RpcClient init, ServerListFactory =com.alibaba.nacos.client.naming.core.ServerListManager
23:45:38.840 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79f562a5-9184-4a63-b2d7-e66a4be9275e]Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
23:45:38.841 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79f562a5-9184-4a63-b2d7-e66a4be9275e]Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
23:45:38.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79f562a5-9184-4a63-b2d7-e66a4be9275e] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
23:45:38.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79f562a5-9184-4a63-b2d7-e66a4be9275e] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755877538850_172.21.0.1_58440
23:45:38.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79f562a5-9184-4a63-b2d7-e66a4be9275e]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
23:45:38.961 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79f562a5-9184-4a63-b2d7-e66a4be9275e]Notify connected event to listeners.
23:45:38.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79f562a5-9184-4a63-b2d7-e66a4be9275e]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
23:45:38.962 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
23:45:39.695 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@logictrue-auth@@DEFAULT -> []
23:45:39.704 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-auth@@DEFAULT -> []
23:45:39.710 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9211"]
23:45:39.728 [main] INFO  c.a.n.client.naming - [registerService,112] - [REGISTER-SERVICE] dev registering service logictrue-auth with instance Instance{instanceId='null', ip='**************', port=9211, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
23:45:39.738 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP logictrue-auth **************:9211 register finished
23:45:39.831 [main] INFO  c.l.a.LogicTrueAuthApplication - [logStarted,61] - Started LogicTrueAuthApplication in 7.992 seconds (JVM running for 8.541)
23:45:39.837 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-auth.yml+DEFAULT_GROUP+dev
23:45:39.838 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-auth.yml, group=DEFAULT_GROUP, cnt=1
23:45:39.838 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-auth+DEFAULT_GROUP+dev
23:45:39.839 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-auth, group=DEFAULT_GROUP, cnt=1
23:45:39.839 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-auth-dev.yml+DEFAULT_GROUP+dev
23:45:39.839 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-auth-dev.yml, group=DEFAULT_GROUP, cnt=1
23:45:40.276 [RMI TCP Connection(2)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:45:40.276 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79f562a5-9184-4a63-b2d7-e66a4be9275e]receive server push request,request=NotifySubscriberRequest,requestId=2
23:45:40.283 [nacos-grpc-client-executor-10] INFO  c.a.n.client.naming - [isChangedServiceInfo,235] - new ips(1) service: DEFAULT_GROUP@@logictrue-auth@@DEFAULT -> [{"instanceId":"**************#9211#DEFAULT#DEFAULT_GROUP@@logictrue-auth","ip":"**************","port":9211,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-auth","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
23:45:40.283 [nacos-grpc-client-executor-10] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-auth@@DEFAULT -> [{"instanceId":"**************#9211#DEFAULT#DEFAULT_GROUP@@logictrue-auth","ip":"**************","port":9211,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-auth","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
23:45:40.284 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79f562a5-9184-4a63-b2d7-e66a4be9275e]ack server push request,request=NotifySubscriberRequest,requestId=2
23:45:42.835 [scheduling-1] INFO  c.l.a.c.WxConnectConfig - [builderAccessToken,73] - java.lang.NullPointerException
23:45:42.837 [scheduling-1] INFO  c.l.a.c.WxConnectConfig - [builderAccessToken,73] - java.lang.NullPointerException
23:45:42.839 [scheduling-1] INFO  c.l.a.c.WxConnectConfig - [builderAccessToken,73] - java.lang.NullPointerException
23:47:05.745 [boundedElastic-2] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(1) service: DEFAULT_GROUP@@logictrue-system -> [{"instanceId":"**************#9201#DEFAULT#DEFAULT_GROUP@@logictrue-system","ip":"**************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
23:47:05.746 [boundedElastic-2] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-system -> [{"instanceId":"**************#9201#DEFAULT#DEFAULT_GROUP@@logictrue-system","ip":"**************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
23:47:06.251 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79f562a5-9184-4a63-b2d7-e66a4be9275e]receive server push request,request=NotifySubscriberRequest,requestId=9
23:47:06.251 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79f562a5-9184-4a63-b2d7-e66a4be9275e]ack server push request,request=NotifySubscriberRequest,requestId=9
