19:26:18.082 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.0.Final
19:26:19.667 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:/home/<USER>/nacos/config
19:26:19.671 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of 1ba93bc2-1d82-46fb-a3f5-82f272418bc1_config-0
19:26:19.727 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 28 ms to scan 1 urls, producing 3 keys and 6 values 
19:26:19.765 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 4 keys and 9 values 
19:26:19.781 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 3 keys and 10 values 
19:26:19.978 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 193 ms to scan 228 urls, producing 0 keys and 0 values 
19:26:19.988 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
19:26:20.004 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
19:26:20.014 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
19:26:20.142 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 125 ms to scan 228 urls, producing 0 keys and 0 values 
19:26:20.143 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ba93bc2-1d82-46fb-a3f5-82f272418bc1_config-0]RpcClient init label, labels={module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:26:20.144 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ba93bc2-1d82-46fb-a3f5-82f272418bc1_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$331/1992801971
19:26:20.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ba93bc2-1d82-46fb-a3f5-82f272418bc1_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/1681094402
19:26:20.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ba93bc2-1d82-46fb-a3f5-82f272418bc1_config-0]Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:26:20.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ba93bc2-1d82-46fb-a3f5-82f272418bc1_config-0]RpcClient init, ServerListFactory =com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:26:20.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ba93bc2-1d82-46fb-a3f5-82f272418bc1_config-0] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
19:26:20.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ba93bc2-1d82-46fb-a3f5-82f272418bc1_config-0] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755170780523_172.21.0.1_45814
19:26:20.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ba93bc2-1d82-46fb-a3f5-82f272418bc1_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:26:20.753 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ba93bc2-1d82-46fb-a3f5-82f272418bc1_config-0]Notify connected event to listeners.
19:26:20.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ba93bc2-1d82-46fb-a3f5-82f272418bc1_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
19:26:20.753 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,681] - [1ba93bc2-1d82-46fb-a3f5-82f272418bc1_config-0] Connected,notify listen context...
19:26:20.787 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,56] - limitTime:5.0
19:26:20.815 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
19:26:20.876 [main] INFO  c.l.a.LogicTrueAuthApplication - [logStartupProfileInfo,663] - The following profiles are active: dev
19:26:22.837 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9211"]
19:26:22.838 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:26:22.838 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
19:26:22.925 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:26:24.496 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.17.5
19:26:24.575 [redisson-netty-5-5] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
19:26:24.641 [redisson-netty-5-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
19:26:24.975 [main] INFO  c.a.c.s.SentinelWebAutoConfiguration - [addInterceptors,80] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:26:26.164 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,63] - initializer namespace from System Property : null
19:26:26.165 [main] INFO  c.a.n.client.naming - [call,69] - initializer namespace from System Environment :null
19:26:26.166 [main] INFO  c.a.n.client.naming - [call,79] - initializer namespace from System Property :null
19:26:26.187 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of 9bffc502-2d23-471d-a7f1-693cc0aaa816
19:26:26.188 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bffc502-2d23-471d-a7f1-693cc0aaa816]RpcClient init label, labels={module=naming, source=sdk}
19:26:26.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bffc502-2d23-471d-a7f1-693cc0aaa816]RpcClient init, ServerListFactory =com.alibaba.nacos.client.naming.core.ServerListManager
19:26:26.190 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bffc502-2d23-471d-a7f1-693cc0aaa816]Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:26:26.190 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bffc502-2d23-471d-a7f1-693cc0aaa816]Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:26:26.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bffc502-2d23-471d-a7f1-693cc0aaa816] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
19:26:26.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bffc502-2d23-471d-a7f1-693cc0aaa816] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755170786200_172.21.0.1_40980
19:26:26.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bffc502-2d23-471d-a7f1-693cc0aaa816]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:26:26.309 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bffc502-2d23-471d-a7f1-693cc0aaa816]Notify connected event to listeners.
19:26:26.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bffc502-2d23-471d-a7f1-693cc0aaa816]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
19:26:26.309 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
19:26:28.131 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@logictrue-auth@@DEFAULT -> []
19:26:28.138 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-auth@@DEFAULT -> []
19:26:28.142 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9211"]
19:26:28.154 [main] INFO  c.a.n.client.naming - [registerService,112] - [REGISTER-SERVICE] dev registering service logictrue-auth with instance Instance{instanceId='null', ip='**************', port=9211, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
19:26:28.165 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP logictrue-auth **************:9211 register finished
19:26:28.658 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bffc502-2d23-471d-a7f1-693cc0aaa816]receive server push request,request=NotifySubscriberRequest,requestId=24
19:26:28.664 [nacos-grpc-client-executor-9] INFO  c.a.n.client.naming - [isChangedServiceInfo,235] - new ips(1) service: DEFAULT_GROUP@@logictrue-auth@@DEFAULT -> [{"instanceId":"**************#9211#DEFAULT#DEFAULT_GROUP@@logictrue-auth","ip":"**************","port":9211,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-auth","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
19:26:28.665 [nacos-grpc-client-executor-9] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-auth@@DEFAULT -> [{"instanceId":"**************#9211#DEFAULT#DEFAULT_GROUP@@logictrue-auth","ip":"**************","port":9211,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-auth","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
19:26:28.665 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bffc502-2d23-471d-a7f1-693cc0aaa816]ack server push request,request=NotifySubscriberRequest,requestId=24
19:26:29.241 [main] INFO  c.l.a.LogicTrueAuthApplication - [logStarted,61] - Started LogicTrueAuthApplication in 12.946 seconds (JVM running for 13.637)
19:26:29.250 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-auth.yml+DEFAULT_GROUP+dev
19:26:29.252 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-auth.yml, group=DEFAULT_GROUP, cnt=1
19:26:29.252 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-auth+DEFAULT_GROUP+dev
19:26:29.253 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-auth, group=DEFAULT_GROUP, cnt=1
19:26:29.254 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-auth-dev.yml+DEFAULT_GROUP+dev
19:26:29.254 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-auth-dev.yml, group=DEFAULT_GROUP, cnt=1
19:26:29.486 [RMI TCP Connection(2)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:26:32.248 [scheduling-1] INFO  c.l.a.c.WxConnectConfig - [builderAccessToken,73] - java.lang.NullPointerException
19:26:32.252 [scheduling-1] INFO  c.l.a.c.WxConnectConfig - [builderAccessToken,73] - java.lang.NullPointerException
19:26:32.256 [scheduling-1] INFO  c.l.a.c.WxConnectConfig - [builderAccessToken,73] - java.lang.NullPointerException
19:45:33.000 [boundedElastic-2] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(1) service: DEFAULT_GROUP@@logictrue-system -> [{"instanceId":"**************#9201#DEFAULT#DEFAULT_GROUP@@logictrue-system","ip":"**************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
19:45:33.001 [boundedElastic-2] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-system -> [{"instanceId":"**************#9201#DEFAULT#DEFAULT_GROUP@@logictrue-system","ip":"**************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
19:45:33.558 [nacos-grpc-client-executor-317] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bffc502-2d23-471d-a7f1-693cc0aaa816]receive server push request,request=NotifySubscriberRequest,requestId=34
19:45:33.559 [nacos-grpc-client-executor-317] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bffc502-2d23-471d-a7f1-693cc0aaa816]ack server push request,request=NotifySubscriberRequest,requestId=34
22:51:29.934 [nacos-grpc-client-executor-389] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ba93bc2-1d82-46fb-a3f5-82f272418bc1_config-0]receive server push request,request=ClientDetectionRequest,requestId=41
22:51:29.934 [nacos-grpc-client-executor-389] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ba93bc2-1d82-46fb-a3f5-82f272418bc1_config-0]ack server push request,request=ClientDetectionRequest,requestId=41
22:51:29.940 [nacos-grpc-client-executor-383] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bffc502-2d23-471d-a7f1-693cc0aaa816]receive server push request,request=ClientDetectionRequest,requestId=40
22:51:29.940 [nacos-grpc-client-executor-383] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bffc502-2d23-471d-a7f1-693cc0aaa816]ack server push request,request=ClientDetectionRequest,requestId=40
