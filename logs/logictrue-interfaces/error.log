23:46:12.463 [main] ERROR c.l.i.utils.WorkUtil - [init,62] - 加载工时MQTT客户端失败！
23:46:16.325 [main] ERROR c.l.i.i.c.MagicModuleConfiguration - [magicSqlModule,176] - 创建db模块
23:46:21.633 [ForkJoinPool.commonPool-worker-49] ERROR c.l.i.LdfBusinessConfig - [lambda$register$5,171] - 注册应用测试流程-生产通知数据失败,失败模型 c3501b3a068deba4b477c1ea6257b5d2(生产通知),将导致应用部分功能不可用，请及时处理！
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [SELECT MAX(prod_notification_info_oder) AS prod_notification_info_oder, MAX(create_time) AS create_time FROM prod_notification_info]; nested exception is java.sql.SQLSyntaxErrorException: Table 'ldf_server.prod_notification_info' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1541)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:393)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:465)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:475)
	at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:508)
	at org.springframework.jdbc.core.JdbcTemplate.queryForMap(JdbcTemplate.java:502)
	at com.logictrue.interfaces.TableInfoConfig.register(TableInfoConfig.java:112)
	at com.logictrue.interfaces.LdfBusinessConfig.lambda$register$5(LdfBusinessConfig.java:169)
	at java.util.ArrayList.forEach(ArrayList.java:1257)
	at com.logictrue.interfaces.LdfBusinessConfig.register(LdfBusinessConfig.java:166)
	at com.logictrue.interfaces.config.LdfBusinessInfoConfig.lambda$loadDevelopApplication$29(LdfBusinessInfoConfig.java:269)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1382)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:481)
	at java.util.stream.ForEachOps$ForEachTask.compute(ForEachOps.java:291)
	at java.util.concurrent.CountedCompleter.exec(CountedCompleter.java:731)
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:289)
	at java.util.concurrent.ForkJoinPool$WorkQueue.runTask(ForkJoinPool.java:1056)
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1692)
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:157)
Caused by: java.sql.SQLSyntaxErrorException: Table 'ldf_server.prod_notification_info' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1202)
	at com.alibaba.druid.pool.DruidPooledStatement.executeQuery(DruidPooledStatement.java:308)
	at org.springframework.jdbc.core.JdbcTemplate$1QueryStatementCallback.doInStatement(JdbcTemplate.java:452)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:381)
	... 19 common frames omitted
23:46:21.633 [ForkJoinPool.commonPool-worker-27] ERROR c.l.i.LdfBusinessConfig - [lambda$register$5,171] - 注册应用简单审批流数据失败,失败模型 908903bb66c9b8314dfb62a3c992bbbd(简易流程提交界面),将导致应用部分功能不可用，请及时处理！
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [SELECT MAX(demoCommitForms_oder) AS demoCommitForms_oder, MAX(create_time) AS create_time FROM demoCommitForms]; nested exception is java.sql.SQLSyntaxErrorException: Table 'ldf_server.demoCommitForms' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1541)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:393)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:465)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:475)
	at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:508)
	at org.springframework.jdbc.core.JdbcTemplate.queryForMap(JdbcTemplate.java:502)
	at com.logictrue.interfaces.TableInfoConfig.register(TableInfoConfig.java:112)
	at com.logictrue.interfaces.LdfBusinessConfig.lambda$register$5(LdfBusinessConfig.java:169)
	at java.util.ArrayList.forEach(ArrayList.java:1257)
	at com.logictrue.interfaces.LdfBusinessConfig.register(LdfBusinessConfig.java:166)
	at com.logictrue.interfaces.config.LdfBusinessInfoConfig.lambda$loadDevelopApplication$29(LdfBusinessInfoConfig.java:269)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1382)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:481)
	at java.util.stream.ForEachOps$ForEachTask.compute(ForEachOps.java:291)
	at java.util.concurrent.CountedCompleter.exec(CountedCompleter.java:731)
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:289)
	at java.util.concurrent.ForkJoinPool$WorkQueue.runTask(ForkJoinPool.java:1056)
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1692)
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:157)
Caused by: java.sql.SQLSyntaxErrorException: Table 'ldf_server.demoCommitForms' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1202)
	at com.alibaba.druid.pool.DruidPooledStatement.executeQuery(DruidPooledStatement.java:308)
	at org.springframework.jdbc.core.JdbcTemplate$1QueryStatementCallback.doInStatement(JdbcTemplate.java:452)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:381)
	... 19 common frames omitted
23:46:21.852 [ForkJoinPool.commonPool-worker-52] ERROR c.l.i.LdfBusinessConfig - [lambda$register$5,171] - 注册应用ADMIN新增业务数据失败,失败模型 bc43785f9ea24c74a3d2b5e7d80307a1(测试编码生成2),将导致应用部分功能不可用，请及时处理！
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [SELECT MAX(builder2_oder) AS builder2_oder, MAX(create_time) AS create_time FROM builder2]; nested exception is java.sql.SQLSyntaxErrorException: Table 'ldf_server.builder2' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1541)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:393)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:465)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:475)
	at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:508)
	at org.springframework.jdbc.core.JdbcTemplate.queryForMap(JdbcTemplate.java:502)
	at com.logictrue.interfaces.TableInfoConfig.register(TableInfoConfig.java:112)
	at com.logictrue.interfaces.LdfBusinessConfig.lambda$register$5(LdfBusinessConfig.java:169)
	at java.util.ArrayList.forEach(ArrayList.java:1257)
	at com.logictrue.interfaces.LdfBusinessConfig.register(LdfBusinessConfig.java:166)
	at com.logictrue.interfaces.config.LdfBusinessInfoConfig.lambda$loadDevelopApplication$29(LdfBusinessInfoConfig.java:269)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1382)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:481)
	at java.util.stream.ForEachOps$ForEachTask.compute(ForEachOps.java:291)
	at java.util.concurrent.CountedCompleter.exec(CountedCompleter.java:731)
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:289)
	at java.util.concurrent.ForkJoinPool$WorkQueue.runTask(ForkJoinPool.java:1056)
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1692)
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:157)
Caused by: java.sql.SQLSyntaxErrorException: Table 'ldf_server.builder2' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1202)
	at com.alibaba.druid.pool.DruidPooledStatement.executeQuery(DruidPooledStatement.java:308)
	at org.springframework.jdbc.core.JdbcTemplate$1QueryStatementCallback.doInStatement(JdbcTemplate.java:452)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:381)
	... 19 common frames omitted
23:46:22.420 [ForkJoinPool.commonPool-worker-7] ERROR c.l.i.LdfBusinessConfig - [lambda$register$5,171] - 注册应用简单审批流数据失败,失败模型 c86bc397ed4d203dae835670388cb081(简易流程提交界面),将导致应用部分功能不可用，请及时处理！
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [SELECT MAX(demoCommitForms_oder) AS demoCommitForms_oder, MAX(create_time) AS create_time FROM demoCommitForms]; nested exception is java.sql.SQLSyntaxErrorException: Table 'ldf_server.demoCommitForms' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1541)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:393)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:465)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:475)
	at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:508)
	at org.springframework.jdbc.core.JdbcTemplate.queryForMap(JdbcTemplate.java:502)
	at com.logictrue.interfaces.TableInfoConfig.register(TableInfoConfig.java:112)
	at com.logictrue.interfaces.LdfBusinessConfig.lambda$register$5(LdfBusinessConfig.java:169)
	at java.util.ArrayList.forEach(ArrayList.java:1257)
	at com.logictrue.interfaces.LdfBusinessConfig.register(LdfBusinessConfig.java:166)
	at com.logictrue.interfaces.LdfBusinessConfig.register(LdfBusinessConfig.java:86)
	at com.logictrue.interfaces.config.LdfBusinessInfoConfig.lambda$loadProductApplication$56(LdfBusinessInfoConfig.java:416)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1382)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:481)
	at java.util.stream.ForEachOps$ForEachTask.compute(ForEachOps.java:291)
	at java.util.concurrent.CountedCompleter.exec(CountedCompleter.java:731)
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:289)
	at java.util.concurrent.ForkJoinPool$WorkQueue.runTask(ForkJoinPool.java:1056)
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1692)
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:157)
Caused by: java.sql.SQLSyntaxErrorException: Table 'ldf_server.demoCommitForms' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1202)
	at com.alibaba.druid.pool.DruidPooledStatement.executeQuery(DruidPooledStatement.java:308)
	at org.springframework.jdbc.core.JdbcTemplate$1QueryStatementCallback.doInStatement(JdbcTemplate.java:452)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:381)
	... 20 common frames omitted
23:52:48.751 [asyncExecutor-1] ERROR o.a.l.AmbiguityLibrary - [error,58] - Init ambiguity library error :org.ansj.exception.LibraryException:  path :library/ambiguity.dic file:/home/<USER>/logicture2/library/ambiguity.dic not found or can not to read, path: library/ambiguity.dic
23:52:48.753 [asyncExecutor-1] ERROR o.a.l.DicLibrary - [error,58] - Init dic library error :org.ansj.exception.LibraryException:  path :library/default.dic file:/home/<USER>/logicture2/library/default.dic not found or can not to read, path: library/default.dic
23:52:54.162 [http-nio-9210-exec-9] ERROR c.l.i.utils.WorkUtil - [syncPushInfo,86] - Client is not connected
23:52:54.176 [http-nio-9210-exec-9] ERROR c.l.i.utils.WorkUtil - [syncPushInfo,86] - Client is not connected
23:53:36.737 [pool-12-thread-1] ERROR c.l.i.utils.WorkUtil - [syncPushInfo,86] - Client is not connected
23:54:38.915 [pool-12-thread-1] ERROR c.l.i.utils.WorkUtil - [syncPushInfo,86] - Client is not connected
23:55:39.989 [pool-12-thread-1] ERROR c.l.i.utils.WorkUtil - [syncPushInfo,86] - Client is not connected
23:56:11.293 [http-nio-9210-exec-8] ERROR c.l.i.utils.WorkUtil - [syncPushInfo,86] - Client is not connected
23:56:11.303 [pool-6-thread-1] ERROR c.l.i.utils.WorkUtil - [syncPushInfo,86] - Client is not connected
23:56:11.306 [pool-6-thread-1] ERROR c.l.i.utils.WorkUtil - [syncPushInfo,86] - Client is not connected
23:56:11.325 [pool-6-thread-1] ERROR c.l.i.s.i.LdfBusinessInfoServiceImpl - [lambda$upBusiness$23,858] - 保存表单执行时间：1
23:56:11.356 [pool-6-thread-1] ERROR c.l.i.s.i.LdfBusinessInfoServiceImpl - [lambda$upBusiness$23,862] - 保存接口执行时间：32
23:56:11.356 [pool-6-thread-1] ERROR c.l.i.s.i.LdfBusinessInfoServiceImpl - [lambda$upBusiness$23,864] - 保存定时任务执行时间：32
23:56:11.357 [pool-6-thread-1] ERROR c.l.i.s.i.LdfBusinessInfoServiceImpl - [lambda$upBusiness$23,866] - 保存流程执行时间：33
23:56:11.357 [pool-6-thread-1] ERROR c.l.i.s.i.LdfBusinessInfoServiceImpl - [lambda$upBusiness$23,868] - 保存条码执行时间：33
23:56:11.357 [pool-6-thread-1] ERROR c.l.i.s.i.LdfBusinessInfoServiceImpl - [lambda$upBusiness$23,870] - 保存模型执行时间：33
23:56:11.357 [pool-6-thread-1] ERROR c.l.i.s.i.LdfBusinessInfoServiceImpl - [lambda$upBusiness$23,872] - 保存触发器执行时间：33
23:56:11.379 [pool-6-thread-1] ERROR c.l.i.s.i.LdfBusinessInfoServiceImpl - [lambda$upBusiness$23,874] - 保存表表执行时间：55
23:56:11.429 [pool-6-thread-1] ERROR c.l.i.utils.WorkUtil - [syncPushInfo,86] - Client is not connected
23:56:16.444 [http-nio-9210-exec-5] ERROR c.l.i.utils.WorkUtil - [syncPushInfo,86] - Client is not connected
23:56:41.910 [pool-12-thread-1] ERROR c.l.i.utils.WorkUtil - [syncPushInfo,86] - Client is not connected
23:57:44.304 [pool-12-thread-1] ERROR c.l.i.utils.WorkUtil - [syncPushInfo,86] - Client is not connected
23:58:36.336 [http-nio-9210-exec-1] ERROR c.l.i.utils.WorkUtil - [syncPushInfo,86] - Client is not connected
23:58:36.342 [http-nio-9210-exec-1] ERROR c.l.i.utils.WorkUtil - [syncPushInfo,86] - Client is not connected
23:58:46.261 [pool-12-thread-1] ERROR c.l.i.utils.WorkUtil - [syncPushInfo,86] - Client is not connected
23:59:47.244 [pool-12-thread-1] ERROR c.l.i.utils.WorkUtil - [syncPushInfo,86] - Client is not connected
