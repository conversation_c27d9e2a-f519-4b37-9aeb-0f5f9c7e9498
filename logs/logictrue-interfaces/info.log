00:00:23.875 [pool-12-thread-1] INFO  c.l.i.t.BackupDataCleanTask - [lambda$configureTasks$0,29] - 执行备份数据清理定时任务: 00:00:23.875
00:00:23.890 [pool-12-thread-1] INFO  c.l.i.s.i.BackupDataServiceImpl - [delBackupData,140] - module:[view],templateId:[null],删除过期备份数据数量：[358]
00:00:23.893 [pool-12-thread-1] INFO  c.l.i.t.BackupDataCleanTask - [lambda$configureTasks$1,36] - 定时清理备份数据cron表达式：0 0 0 ? * *
00:14:30.375 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,90] - De-registering from Nacos Server now...
00:14:30.376 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [deregisterService,136] - [DEREGISTER-SERVICE] dev deregistering service logictrue-interfaces with instance: Instance{instanceId='null', ip='**************', port=9210, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
00:14:30.380 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,110] - De-registration finished.
00:14:30.383 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,256] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
00:14:30.384 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,140] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
00:14:30.384 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,142] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
00:14:30.384 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,258] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
00:14:30.385 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,176] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
00:14:30.385 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,130] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
00:14:30.686 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,132] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
00:14:30.686 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,188] - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
00:14:30.686 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,193] - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
00:14:30.687 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,519] - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
00:14:30.687 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,162] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
00:14:30.687 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,164] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
00:14:30.687 [SpringApplicationShutdownHook] INFO  c.a.n.c.i.CredentialWatcher - [stop,105] - [null] CredentialWatcher is stopped
00:14:30.687 [SpringApplicationShutdownHook] INFO  c.a.n.c.i.CredentialService - [free,99] - [null] CredentialService is freed
00:14:30.687 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,523] - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
00:14:30.687 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,461] - Shutdown rpc client ,set status to shutdown
00:14:30.687 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,463] - Shutdown  client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7c59422[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
00:14:30.687 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,465] - Close current connection 1755877544592_172.21.0.1_58448
00:14:30.689 [nacos-grpc-client-executor-426] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755877544592_172.21.0.1_58448]Ignore complete event,isRunning:false,isAbandon=false
00:14:30.693 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,83] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5c81ca9a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 427]
00:14:30.693 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,251] - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@1ece9105[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 557]
00:14:30.694 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,182] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
00:14:30.727 [SpringApplicationShutdownHook] INFO  c.l.i.u.ElasticSearchClient - [close,133] - Elasticsearch客户端已关闭
00:14:30.728 [SpringApplicationShutdownHook] INFO  o.a.i.s.p.SessionPool - [close,568] - closing the session pool, cleaning queues...
00:14:30.757 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2073] - {dataSource-1} closing ...
00:14:30.763 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2146] - {dataSource-1} closed
00:14:30.932 [SpringApplicationShutdownHook] INFO  o.m.d.connection - [info,71] - Closed connection [connectionId{localValue:3, serverValue:48201}] to localhost:27017 because the pool has been closed.
00:14:36.627 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.0.Final
00:14:37.325 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:/home/<USER>/nacos/config
00:14:37.329 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of 97e50efb-372e-49dc-9993-04d68d65fc37_config-0
00:14:37.384 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 
00:14:37.420 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
00:14:37.433 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
00:14:37.686 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 249 ms to scan 419 urls, producing 0 keys and 0 values 
00:14:37.693 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
00:14:37.704 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
00:14:37.712 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
00:14:37.930 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 216 ms to scan 419 urls, producing 0 keys and 0 values 
00:14:37.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97e50efb-372e-49dc-9993-04d68d65fc37_config-0]RpcClient init label, labels={module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
00:14:37.936 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97e50efb-372e-49dc-9993-04d68d65fc37_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$359/354154358
00:14:37.936 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97e50efb-372e-49dc-9993-04d68d65fc37_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$360/1517105378
00:14:37.937 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97e50efb-372e-49dc-9993-04d68d65fc37_config-0]Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
00:14:37.938 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97e50efb-372e-49dc-9993-04d68d65fc37_config-0]RpcClient init, ServerListFactory =com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
00:14:37.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97e50efb-372e-49dc-9993-04d68d65fc37_config-0] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
00:14:38.550 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97e50efb-372e-49dc-9993-04d68d65fc37_config-0] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755879278375_172.21.0.1_53426
00:14:38.551 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97e50efb-372e-49dc-9993-04d68d65fc37_config-0]Notify connected event to listeners.
00:14:38.551 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,681] - [97e50efb-372e-49dc-9993-04d68d65fc37_config-0] Connected,notify listen context...
00:14:38.551 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97e50efb-372e-49dc-9993-04d68d65fc37_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
00:14:38.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97e50efb-372e-49dc-9993-04d68d65fc37_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
00:14:38.593 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,56] - limitTime:5.0
00:14:38.610 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
00:14:38.670 [main] INFO  c.l.i.LogicTrueInterfacesApplication - [logStartupProfileInfo,663] - The following profiles are active: dev
00:14:43.010 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9210"]
00:14:43.011 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:14:43.011 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:14:43.160 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:14:43.781 [main] INFO  o.m.driver.cluster - [info,71] - Cluster created with settings {hosts=[localhost:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
00:14:43.852 [cluster-ClusterId{value='68a89773d30488768ef4b625', description='null'}-localhost:27017] INFO  o.m.d.connection - [info,71] - Opened connection [connectionId{localValue:2, serverValue:49772}] to localhost:27017
00:14:43.852 [cluster-rtt-ClusterId{value='68a89773d30488768ef4b625', description='null'}-localhost:27017] INFO  o.m.d.connection - [info,71] - Opened connection [connectionId{localValue:1, serverValue:49773}] to localhost:27017
00:14:43.853 [cluster-ClusterId{value='68a89773d30488768ef4b625', description='null'}-localhost:27017] INFO  o.m.driver.cluster - [info,71] - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=25, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=37363736}
00:14:44.805 [main] INFO  o.m.d.connection - [info,71] - Opened connection [connectionId{localValue:3, serverValue:49774}] to localhost:27017
00:14:45.465 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,63] - initializer namespace from System Property : null
00:14:45.467 [main] INFO  c.a.n.client.naming - [call,69] - initializer namespace from System Environment :null
00:14:45.467 [main] INFO  c.a.n.client.naming - [call,79] - initializer namespace from System Property :null
00:14:45.483 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of 7b23f3ae-0e22-4a6f-84ba-4bda297bdcf5
00:14:45.483 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b23f3ae-0e22-4a6f-84ba-4bda297bdcf5]RpcClient init label, labels={module=naming, source=sdk}
00:14:45.486 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b23f3ae-0e22-4a6f-84ba-4bda297bdcf5]RpcClient init, ServerListFactory =com.alibaba.nacos.client.naming.core.ServerListManager
00:14:45.486 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b23f3ae-0e22-4a6f-84ba-4bda297bdcf5]Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
00:14:45.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b23f3ae-0e22-4a6f-84ba-4bda297bdcf5]Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
00:14:45.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b23f3ae-0e22-4a6f-84ba-4bda297bdcf5] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
00:14:45.597 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b23f3ae-0e22-4a6f-84ba-4bda297bdcf5] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755879285493_172.21.0.1_56250
00:14:45.598 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b23f3ae-0e22-4a6f-84ba-4bda297bdcf5]Notify connected event to listeners.
00:14:45.598 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b23f3ae-0e22-4a6f-84ba-4bda297bdcf5]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
00:14:45.598 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
00:14:45.599 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b23f3ae-0e22-4a6f-84ba-4bda297bdcf5]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
00:14:46.016 [main] INFO  c.a.d.p.DruidDataSource - [init,995] - {dataSource-1} inited
00:14:49.033 [main] INFO  c.a.c.s.SentinelWebAutoConfiguration - [addInterceptors,80] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
00:14:50.186 [main] INFO  c.g.d.c.DozerBeanMapperBuilder - [build,544] - Initializing Dozer. Version: 6.4.0, Thread Name: main
00:14:50.187 [main] INFO  c.g.d.c.u.RuntimeUtils - [isOSGi,53] - OSGi support is false
00:14:50.193 [main] INFO  c.g.d.c.c.r.LegacyPropertiesSettingsResolver - [processFile,60] - Trying to find Dozer configuration file: dozer.properties
00:14:50.200 [main] INFO  c.g.d.c.c.r.LegacyPropertiesSettingsResolver - [processFile,63] - Failed to find dozer.properties via com.github.dozermapper.core.config.resolvers.LegacyPropertiesSettingsResolver.
00:14:50.759 [main] INFO  c.l.i.i.d.m.MagicDynamicDataSource - [put,73] - 注册数据源：default
00:15:15.132 [main] INFO  c.l.i.e.s.MeterReadingTask - [Reading,125] - 能耗数据库迁移IoTDb功能状态：关闭
00:15:15.217 [main] INFO  c.l.i.g.c.e.DataRoomGlobalExceptionHandler - [init,30] - ----------------------------------------
00:15:15.217 [main] INFO  c.l.i.g.c.e.DataRoomGlobalExceptionHandler - [init,31] - 初始化默认全局异常处理，如果和项目中的全局异常处理冲突，可以在配置文件中配置gc.starter.dataroom.component.DataRoomGlobalExceptionHandler=false禁用该全局异常处理
00:15:15.217 [main] INFO  c.l.i.g.c.e.DataRoomGlobalExceptionHandler - [init,32] - ----------------------------------------
00:15:16.316 [main] INFO  c.l.i.u.ElasticSearchClient - [init,104] - 初始化Elasticsearch客户端，连接到 http://************:9200
00:15:16.430 [main] INFO  c.l.i.u.ElasticSearchClient - [init,115] - Elasticsearch客户端初始化成功
00:15:16.826 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of bd8960a3-4259-49ac-837b-9131bb88817d_config-0
00:15:16.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd8960a3-4259-49ac-837b-9131bb88817d_config-0]RpcClient init label, labels={module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
00:15:16.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd8960a3-4259-49ac-837b-9131bb88817d_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$359/354154358
00:15:16.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd8960a3-4259-49ac-837b-9131bb88817d_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$360/1517105378
00:15:16.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd8960a3-4259-49ac-837b-9131bb88817d_config-0]Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
00:15:16.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd8960a3-4259-49ac-837b-9131bb88817d_config-0]RpcClient init, ServerListFactory =com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
00:15:16.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd8960a3-4259-49ac-837b-9131bb88817d_config-0] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
00:15:16.938 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd8960a3-4259-49ac-837b-9131bb88817d_config-0] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755879316834_172.21.0.1_44028
00:15:16.938 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd8960a3-4259-49ac-837b-9131bb88817d_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
00:15:16.938 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd8960a3-4259-49ac-837b-9131bb88817d_config-0]Notify connected event to listeners.
00:15:16.939 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,681] - [bd8960a3-4259-49ac-837b-9131bb88817d_config-0] Connected,notify listen context...
00:15:16.938 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd8960a3-4259-49ac-837b-9131bb88817d_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
00:15:17.406 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.17.5
00:15:17.497 [redisson-netty-5-7] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
00:15:17.534 [redisson-netty-5-18] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
00:15:17.746 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [magicNotifyService,242] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:15:17.819 [main] INFO  c.l.i.i.c.MagicModuleConfiguration - [pageProvider,121] - 未找到分页实现,采用默认分页实现,分页配置:(页码=pageNum,页大小=pageSize,默认首页=1,默认页大小=20,最大页大小=-1)
00:15:17.821 [main] INFO  c.l.i.i.c.MagicModuleConfiguration - [sqlCache,132] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:15:18.069 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [magicConfiguration,325] - magic-api工作目录:db://magic_api_file//interfaces
00:15:18.079 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [setupMagicModules,288] - 注册模块:log -> interface org.slf4j.Logger
00:15:18.104 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:energy -> class com.logictrue.interfaces.energy.Energy
00:15:18.104 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:spc -> class com.logictrue.interfaces.external.spcNew.SpcActuator
00:15:18.104 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:flowModel -> class com.logictrue.interfaces.flow.utils.FlowModel
00:15:18.105 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:md -> class com.logictrue.interfaces.interfaces.modules.model.Model
00:15:18.105 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:auth -> class com.logictrue.interfaces.utils.Auth
00:15:18.105 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:chartUtil -> class com.logictrue.interfaces.utils.ChartUtils
00:15:18.105 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:util -> class com.logictrue.interfaces.utils.CommonUtil
00:15:18.105 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:config -> class com.logictrue.interfaces.utils.Config
00:15:18.105 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:dict -> class com.logictrue.interfaces.utils.Dict
00:15:18.105 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:esClient -> class com.logictrue.interfaces.utils.ElasticSearchClient
00:15:18.105 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:excel -> class com.logictrue.interfaces.utils.ExcelUtils
00:15:18.105 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:file -> class com.logictrue.interfaces.utils.FileUtils
00:15:18.105 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:flow -> class com.logictrue.interfaces.utils.Flow
00:15:18.106 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:GOrder -> class com.logictrue.interfaces.utils.GOrder
00:15:18.106 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:jfreeChartUtil -> class com.logictrue.interfaces.utils.JfreeChartUtil
00:15:18.106 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:littleCodeUtil -> class com.logictrue.interfaces.utils.LittleCodeUtil
00:15:18.106 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:lock -> class com.logictrue.interfaces.utils.LockUtil
00:15:18.106 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:mq -> class com.logictrue.interfaces.utils.MQ
00:15:18.106 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:message -> class com.logictrue.interfaces.utils.Message
00:15:18.106 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:plc -> class com.logictrue.interfaces.utils.Plc
00:15:18.106 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:reset -> class com.logictrue.interfaces.utils.Reset
00:15:18.107 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:Resources -> class com.logictrue.interfaces.utils.Resources
00:15:18.107 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:rpc -> class com.logictrue.interfaces.utils.RpcUtil
00:15:18.107 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:rule -> class com.logictrue.interfaces.utils.RuleEngineUtil
00:15:18.107 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:word -> class com.logictrue.interfaces.utils.WordUtil
00:15:18.107 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:ws -> class com.logictrue.interfaces.utils.Ws
00:15:18.107 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:api -> class com.logictrue.interfaces.utils.MagicApiExe
00:15:18.107 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:lock -> class com.logictrue.interfaces.utils.LockUtil
00:15:18.107 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:db -> class com.logictrue.interfaces.interfaces.modules.db.SQLModule
00:15:18.107 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:http -> class com.logictrue.interfaces.interfaces.modules.http.HttpModule
00:15:18.107 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:env -> class com.logictrue.interfaces.interfaces.modules.spring.EnvModule
00:15:18.107 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:request -> class com.logictrue.interfaces.interfaces.modules.servlet.RequestModule
00:15:18.108 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:response -> class com.logictrue.interfaces.interfaces.modules.servlet.ResponseModule
00:15:18.108 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:magic -> class com.logictrue.interfaces.interfaces.core.service.impl.DefaultMagicAPIService
00:15:18.108 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:mongo -> class com.logictrue.interfaces.interfaces.mongo.MongoModule
00:15:18.108 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,295] - 注册模块:redis -> class com.logictrue.interfaces.interfaces.redis.RedisModule
00:15:18.109 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,303] - 自动导入模块：db
00:15:18.111 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$null$10,311] - 注册扩展:class com.logictrue.interfaces.interfaces.modules.servlet.ResponseModule -> class com.logictrue.interfaces.interfaces.javaee.MagicJavaEEResponseExtension
00:15:18.212 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$magicConfiguration$13,380] - 注册请求拦截器：class com.logictrue.interfaces.interceptor.CustomRequestInterceptor
00:15:18.212 [main] INFO  c.l.i.i.c.MagicAPIAutoConfiguration - [lambda$magicConfiguration$13,380] - 注册请求拦截器：class com.logictrue.interfaces.interceptor.SysLogInterceptor
00:15:20.807 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@logictrue-interfaces@@DEFAULT -> []
00:15:20.816 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-interfaces@@DEFAULT -> []
00:15:20.821 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9210"]
00:15:20.843 [main] INFO  c.a.n.client.naming - [registerService,112] - [REGISTER-SERVICE] dev registering service logictrue-interfaces with instance Instance{instanceId='null', ip='**************', port=9210, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
00:15:20.849 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP logictrue-interfaces **************:9210 register finished
00:15:20.851 [main] INFO  c.l.i.c.LdfBusinessInfoConfig - [start,105] - 开始加载应用信息
00:15:21.197 [main] INFO  c.a.d.p.DruidDataSource - [init,995] - {dataSource-2,ldf_server} inited
00:15:21.292 [main] INFO  c.l.i.c.LdfBusinessInfoConfig - [loadDevelopApplication,135] - 开始加载默认环境工作区应用！
00:15:21.401 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b23f3ae-0e22-4a6f-84ba-4bda297bdcf5]receive server push request,request=NotifySubscriberRequest,requestId=14
00:15:21.408 [nacos-grpc-client-executor-15] INFO  c.a.n.client.naming - [isChangedServiceInfo,235] - new ips(1) service: DEFAULT_GROUP@@logictrue-interfaces@@DEFAULT -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
00:15:21.408 [nacos-grpc-client-executor-15] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-interfaces@@DEFAULT -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
00:15:21.409 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b23f3ae-0e22-4a6f-84ba-4bda297bdcf5]ack server push request,request=NotifySubscriberRequest,requestId=14
00:15:21.549 [main] INFO  c.a.d.p.DruidDataSource - [init,995] - {dataSource-3,ldf_server} inited
00:15:22.566 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 报告溯源查询 应用Id: 8487b2e3823c4d1fb24b0f3a4985535c
00:15:22.569 [main] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 落尘管理 应用Id: d7a489334182581cf194588777ab7d52
00:15:22.569 [ForkJoinPool.commonPool-worker-12] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: hhh 应用Id: 123ac4d60906b3085a9626920ea50fcd
00:15:22.569 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 使用体验 应用Id: f1e0d21d60606410ffd1ee9de312ae2a
00:15:22.571 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 标准物质管理 应用Id: a1c8d38efdb0c78aa01b79f24b022cbf
00:15:22.572 [ForkJoinPool.commonPool-worker-15] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 设备台账 应用Id: 1da8861973c48276c74d95d605cfc8ac
00:15:22.572 [ForkJoinPool.commonPool-worker-11] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 品牌 应用Id: 807430741964b3e68775afad4591e22b
00:15:22.572 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 检测依据管理 应用Id: 1fd25061fd6d9902cee29d333c130691
00:15:22.572 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 文件管理 应用Id: 14976b80083715317de7b4ba01cc9857
00:15:22.575 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: GG 应用Id: 9a07d2fc41b641c2f7edaa437d2428d1
00:15:22.577 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 故障分析与排查 应用Id: 66a1a1017570325ff0e16f6774ad2a02
00:15:22.577 [ForkJoinPool.commonPool-worker-6] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: dwq 应用Id: 162f25aaafe02d3976b45734f02d7541
00:15:22.585 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 车间 应用Id: 9f137410ecc68cfceab2d4c63b14019c
00:15:22.583 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 计算公式管理 应用Id: 1c62b48bc35cbd429741e7e4ced57d4b
00:15:22.583 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 产线管理 应用Id: 5860bc9d09d10dd0a6c85901277a1939
00:15:22.592 [ForkJoinPool.commonPool-worker-11] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用品牌,注册视图：品牌视图管理
00:15:22.592 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用车间,注册视图：车间管理
00:15:22.592 [ForkJoinPool.commonPool-worker-12] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用hhh,注册视图：148
00:15:22.594 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用标准物质管理,注册视图：基准物质配置列表（基础配置）
00:15:22.594 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用产线管理,注册视图：产线管理
00:15:22.594 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用计算公式管理,注册视图：计算公式管理列表
00:15:22.595 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检测依据管理,注册视图：检测依据列表
00:15:22.595 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用故障分析与排查,注册视图：故障分析与排查
00:15:22.595 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用落尘管理,注册视图：落尘点位
00:15:22.595 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件档案柜号列表
00:15:22.596 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用使用体验,注册视图：设备类型
00:15:22.596 [ForkJoinPool.commonPool-worker-15] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备台账,注册接口：a7c330a9c3a02d3416136406beafc8ef
00:15:22.610 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: JJJJ 应用Id: 2761e599250d36b859e7aee78cae8277
00:15:22.610 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: SPC分析管理 应用Id: 1e2700e672843b81cc88dd92f34cc73b
00:15:22.610 [ForkJoinPool.commonPool-worker-6] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: cccc 应用Id: ad07f86af2590ac3a2b67067e3622202
00:15:22.610 [ForkJoinPool.commonPool-worker-23] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 测试 应用Id: 79f48e16385bfa77d96d94631641d115
00:15:22.610 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 检测方法管理 应用Id: 0271612ba6f8491af3e68c916bafac39
00:15:22.609 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 设备基础数据 应用Id: 8013aabeba93ae686d518c7290c3ee00
00:15:22.609 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: JJJJ 应用Id: ceb2b1c7ccd9d0c3e2a9bdc19eba4cd6
00:15:22.610 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 单位管理 应用Id: 67ef199cfc5762c59584d07ed8f2b560
00:15:22.611 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: WW 应用Id: 0aac74387dc9f6b15d13604546d43e94
00:15:22.611 [ForkJoinPool.commonPool-worker-30] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 设备报修 应用Id: 12ef3951f062d00cef0848c4893bbba5
00:15:22.612 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 流程测试 应用Id: fbd2e1e4454747b983321d6c9ee6d86f
00:15:22.615 [ForkJoinPool.commonPool-worker-23] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试,注册视图：编码生成
00:15:22.615 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据,注册视图：检定记录列表
00:15:22.615 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检测方法管理,注册视图：检测方法管理列表
00:15:22.615 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用单位管理,注册接口：0b4d881cae9901cdb26e4408cdf23cf6
00:15:22.616 [ForkJoinPool.commonPool-worker-30] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备报修,注册视图：设备报修提交
00:15:22.616 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 质量监控 应用Id: 105aab38211caf5555a7579a6b3c2fdc
00:15:22.616 [ForkJoinPool.commonPool-worker-6] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 包装房温湿度记录 应用Id: 19ddc56be9f51d51de5c1ef377259b5d
00:15:22.616 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: JJJJ 应用Id: 4324edfd1f8c99bdb4229c039ae5010a
00:15:22.616 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用WW,注册视图：154
00:15:22.616 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: QQ 应用Id: c87d6c8247cd5c6f1a87c2a10fe2aa23
00:15:22.619 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 工作量统计查询 应用Id: b6a75d3c3cf845772984200049bc7836
00:15:22.617 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用流程测试,注册视图：qwd
00:15:22.628 [ForkJoinPool.commonPool-worker-6] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用包装房温湿度记录,注册视图：包房温湿度
00:15:22.629 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用QQ,注册视图：qwc
00:15:22.629 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: cdb 应用Id: 6f899a02e1ed007b20b619795f21a13e
00:15:22.629 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 描述描述 应用Id: 0c61124f8639ada29ce31ae90da2236a
00:15:22.631 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 测试开发模块-lyh 应用Id: 0f3139ea6b691263b0bd29a55fd3a38f
00:15:22.633 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 仓库设置 应用Id: 088693ad69df5c7bfda3421f084395da
00:15:22.636 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: cdb 应用Id: c1867aaed4b8e44ab83795357d084534
00:15:22.637 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 巡检保修 应用Id: 10ed2c68cc08747978cb8ccbd2a1d975
00:15:22.636 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: ADMIN新增业务 应用Id: 98c0313cabae2fe98e2cda652cbfa94c
00:15:22.639 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 化验岗位管理 应用Id: 5df48a2b2a2886e1b024374b57cf29c6
00:15:22.640 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仓库设置,注册视图：货架管理列表
00:15:22.640 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用cdb,注册视图：模型测试
00:15:22.641 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 商务合同 应用Id: e600e4a29c9f6caa724e9a6587ccfbf5
00:15:22.643 [ForkJoinPool.commonPool-worker-30] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备报修,注册视图：故障等级管理模板
00:15:22.643 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 工艺模型_wjtest 应用Id: 14bb9544f36cfddce63bbaa9b2822ad2
00:15:22.646 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 报告流程管理 应用Id: 671d04d5e56881534425f15145b42ed0
00:15:22.646 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 设备报废 应用Id: daaf350dc2d51fcbab52ea1ed4cc78ba
00:15:22.646 [ForkJoinPool.commonPool-worker-11] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用品牌,注册视图：品牌
00:15:22.649 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件档案柜位列表
00:15:22.648 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用流程测试,注册视图：asd
00:15:22.649 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用QQ,注册视图：大啊撒打算
00:15:22.649 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用WW,注册接口：803c75b283d48205e30a3e002621a1ec
00:15:22.649 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用ADMIN新增业务,注册视图：12e
00:15:22.650 [ForkJoinPool.commonPool-worker-16] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 不合格处置单 应用Id: 0bec1cdaace54e189958379f8b2a36e1
00:15:22.652 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用车间,注册视图：车间
00:15:22.654 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 仪表区域 应用Id: 101dd5c1981106efa3d5bad3d8c4a0fa
00:15:22.657 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用化验岗位管理,注册视图：化验岗位列表
00:15:22.660 [ForkJoinPool.commonPool-worker-6] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用包装房温湿度记录,注册视图：包装房温度湿度记录
00:15:22.661 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用商务合同,注册视图：合同管理界面
00:15:22.662 [ForkJoinPool.commonPool-worker-25] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 平台BUG 应用Id: c791d848a001970d78ac6bba41acda8b
00:15:22.662 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型_wjtest,注册视图：11
00:15:22.662 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 流程测试应用 应用Id: 20de057a7bb4d50ad81f1d58aa190788
00:15:22.663 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仓库设置,注册视图：仓库管理列表
00:15:22.663 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检测方法管理,注册视图：检测方法管理表单
00:15:22.666 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 寄样管理 应用Id: 7ce544008f0c6f284bcc413f9166e4c5
00:15:22.667 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用标准物质管理,注册视图：标准物质基础配置列表
00:15:22.669 [ForkJoinPool.commonPool-worker-30] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备报修,注册视图：报修类型管理界面
00:15:22.669 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用报告流程管理,注册视图：报告流程列表
00:15:22.669 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备报废,注册视图：设备报废列表
00:15:22.670 [ForkJoinPool.commonPool-worker-16] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用不合格处置单,注册视图：不合格处置单
00:15:22.670 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用计算公式管理,注册视图：计算公式管理表单
00:15:22.670 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件入库列表
00:15:22.674 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用产线管理,注册视图：产线管理表单
00:15:22.674 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据,注册视图：作业流程管理列表
00:15:22.674 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用故障分析与排查,注册视图：故障分析与排查表单
00:15:22.677 [ForkJoinPool.commonPool-worker-12] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用hhh,注册视图：45
00:15:22.678 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用流程测试应用,注册视图：开始流程
00:15:22.678 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用寄样管理,注册视图：寄样管理
00:15:22.678 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检测依据管理,注册视图：检测依据表单
00:15:22.679 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用化验岗位管理,注册视图：化验岗位表单
00:15:22.679 [ForkJoinPool.commonPool-worker-25] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用平台BUG,注册视图：问题反馈
00:15:22.680 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仓库设置,注册视图：货架管理表单
00:15:22.681 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 区域结构配置 应用Id: c1d29480ef5563e55b2cb49dd556b522
00:15:22.681 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用QQ,注册视图：子流程表格
00:15:22.674 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: demo111 应用Id: 64fb4afed8dc594dea2e3a5fe071999c
00:15:22.686 [ForkJoinPool.commonPool-worker-54] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 客户需求管理 应用Id: 80a58e2fadd136b8e3c9ddc21e7e7147
00:15:22.686 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用商务合同,注册视图：商务合同模型
00:15:22.687 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件台账列表
00:15:22.681 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 物料管理 应用Id: 41a46581d8f08c7a1c590c8b141a4f00
00:15:22.690 [ForkJoinPool.commonPool-worker-30] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备报修,注册视图：故障等级
00:15:22.690 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用报告流程管理,注册视图：报告流程管理表单
00:15:22.690 [ForkJoinPool.commonPool-worker-12] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用hhh,注册视图：oo
00:15:22.690 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用ADMIN新增业务,注册视图：行内编辑增加编码
00:15:22.674 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 基础数据 应用Id: a181392cff5cb2ec049f4a4077a95cc7
00:15:22.694 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用标准物质管理,注册视图：标准物质台账列表
00:15:22.694 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 测试10-08 应用Id: 415ea5f3d7ec44e438479e20cacf66bc
00:15:22.695 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 留样仓管理 应用Id: 372e536d27d5319af717b4ecc27cc1c2
00:15:22.698 [ForkJoinPool.commonPool-worker-33] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 加复测管理 应用Id: 756335e6453bc522a6a135b303034e03
00:15:22.698 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 测试历史记录 应用Id: 2350c337abfe713b7a49c5a6da29bb53
00:15:22.698 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 配液管理 应用Id: 7ddf73f26ad0f769e9f1fd2eecd4cb7a
00:15:22.699 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 能耗分析 应用Id: 07d02f9562c7e9d4e066bc7b5a15b567
00:15:22.699 [ForkJoinPool.commonPool-worker-62] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 成品组批 应用Id: 79d2e3a2cef854c2a3716b806112e846
00:15:22.699 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 物料类型 应用Id: 181e29f2ab34d8eee3a33cc3961e9a98
00:15:22.706 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 统一表格搜索 应用Id: 92d8ade6efccd1aff9f0dfa7f26ec5fa
00:15:22.706 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 工艺模型_wj 应用Id: b08d5782f74bc15303841442675edfcd
00:15:22.708 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 质控样管理 应用Id: 91d7d07c75cf856e44bde93133a89683
00:15:22.708 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 报告流程管理 应用Id: 20b6edc91bb06d1a73db65324a3f7add
00:15:22.709 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 原合同清单 应用Id: 02461dbaf7052749e0dfbecaa6221f40
00:15:22.711 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 测试 应用Id: b187a272d78fb73dc917b7b312aad3cf
00:15:22.712 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 格林美服务 应用Id: 8e549b2f7ae0f954f82df9b90fc9fe35
00:15:22.712 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 物料管理 应用Id: 178dfdff277fed675c9cdc20793a5954
00:15:22.712 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用流程测试应用,注册视图：测试流程
00:15:22.713 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用demo111,注册接口：7f4629d27977b1f7fde2ef5103f3739c
00:15:22.713 [ForkJoinPool.commonPool-worker-54] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用客户需求管理,注册视图：客户需求管理
00:15:22.715 [ForkJoinPool.commonPool-worker-39] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 报告流程管理 应用Id: 0385a01032740616a62958759dc96629
00:15:22.715 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据,注册视图：计量设备管理列表
00:15:22.716 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件发放列表
00:15:22.719 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用ADMIN新增业务,注册视图：测试编码生成器
00:15:22.720 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试10-08,注册视图：测试
00:15:22.720 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料管理,注册视图：物料信息
00:15:22.721 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用能耗分析,注册视图：总能耗分析
00:15:22.723 [ForkJoinPool.commonPool-worker-33] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 铜显色 应用Id: eace0634466e1cf441833ccd1d61367e
00:15:22.723 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用基础数据,注册接口：dc939a4cf28f4ced890284aedaf37d5e
00:15:22.723 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 报告流程管理 应用Id: c96f3c2057814dba29689b763bdca0f0
00:15:22.723 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用留样仓管理,注册视图：样品入库管理表单
00:15:22.723 [ForkJoinPool.commonPool-worker-15] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备台账,注册接口：c705b9af59dd1922852f187c452291e3
00:15:22.725 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用QQ,注册视图：原始记录模版1
00:15:22.726 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备报废,注册视图：设备报废表单
00:15:22.728 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用WW,注册模型：154
00:15:22.729 [ForkJoinPool.commonPool-worker-62] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: MHP仲裁 应用Id: b3ab7a8aa9503b9d2182b9f18027715b
00:15:22.729 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用标准物质管理,注册视图：基准溶液(JZ)配置列表
00:15:22.729 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型_wj,注册视图：工艺模型
00:15:22.729 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试历史记录,注册视图：asdzx
00:15:22.730 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 工序管理 应用Id: f655af3194c020f8bd2d7d3abd0973af
00:15:22.730 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用质控样管理,注册视图：质控样列表
00:15:22.731 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原合同清单,注册视图：测试流程
00:15:22.729 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料类型,注册视图：物料类型列表
00:15:22.733 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用统一表格搜索,注册视图：表格搜索
00:15:22.733 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用配液管理,注册视图：产品规格配置
00:15:22.733 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: ZYX测试业务 应用Id: c7247f8239e79046ffee477f346e6011
00:15:22.735 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: Test 应用Id: 2b9792ed407beb15e68684e7dff62ff7
00:15:22.737 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试,注册视图：物料类型
00:15:22.737 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料管理,注册视图：物料管理
00:15:22.737 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用格林美服务,注册视图：库存列表
00:15:22.739 [ForkJoinPool.commonPool-worker-6] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用包装房温湿度记录,注册视图：包装房温湿度记录
00:15:22.747 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料类型,注册视图：物料类型表单
00:15:22.751 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工序管理,注册视图：工序
00:15:22.753 [ForkJoinPool.commonPool-worker-33] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用铜显色,注册视图：铜显色检测点位
00:15:23.158 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用标准物质管理,注册视图：标准滴定溶液(BY)基础配置列表
00:15:23.159 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 报告流程管理 应用Id: 3f5b7212714e49fa1f2806531e887212
00:15:23.158 [ForkJoinPool.commonPool-worker-39] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 报告流程管理 应用Id: e470e13ce15c6268d8f0b3ea1d3d8395
00:15:23.158 [ForkJoinPool.commonPool-worker-62] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 紧急放行 应用Id: 5f932318927fbd9e9909494bdb4f77df
00:15:23.159 [ForkJoinPool.commonPool-worker-42] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 费用配置 应用Id: 2cf94efe93bc9c8a59c3a56d93c0a85c
00:15:23.159 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件借出列表
00:15:23.158 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用质控样管理,注册视图：质控样表单
00:15:23.159 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用产线管理,注册模型：产线管理表单
00:15:23.160 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 1 应用Id: 2f6af504e40205f820a5a29c020ddd40
00:15:23.160 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 工艺模型-lyh 应用Id: 12cd3fa4c614b567b70698834e1ebf7b
00:15:23.161 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原合同清单,注册视图：简单流程
00:15:23.161 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据,注册视图：计划列表
00:15:23.193 [ForkJoinPool.commonPool-worker-6] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用包装房温湿度记录,注册模型：包装房温度湿度记录
00:15:23.193 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用故障分析与排查,注册模型：故障分析与排查表单
00:15:23.215 [ForkJoinPool.commonPool-worker-39] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 报告流程管理 应用Id: 48c944250b4e39f2641100b41a8ecaee
00:15:23.216 [ForkJoinPool.commonPool-worker-42] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用费用配置,注册视图：价格方案
00:15:23.219 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用标准物质管理,注册视图：标定公式列表
00:15:23.220 [ForkJoinPool.commonPool-worker-12] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用hhh,注册视图：行内编辑增加编码
00:15:23.221 [ForkJoinPool.commonPool-worker-30] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备报修,注册视图：设备报修类型
00:15:23.222 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 金属地图 应用Id: 73d1af5bac964a245b0e2b4b8b4cf483
00:15:23.223 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型-lyh,注册视图：工艺模型
00:15:23.225 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据,注册视图：检定记录模板管理表单
00:15:23.225 [ForkJoinPool.commonPool-worker-62] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 节点样管理 应用Id: 8e57dba599a315b6a7e79244140bdd77
00:15:23.226 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件作废列表
00:15:23.245 [ForkJoinPool.commonPool-worker-11] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用品牌,注册模型：品牌
00:15:23.226 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原合同清单,注册视图：five_origina_contract
00:15:23.253 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型_wjtest,注册视图：22
00:15:23.256 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试10-08,注册模型：测试
00:15:23.256 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 异常处置单 应用Id: 719dfc6d8788122649e126671e82e230
00:15:23.256 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 测试外部条码 应用Id: 2a8afc121b99dad209794c848e11fc0f
00:15:23.257 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试历史记录,注册视图：qweaaaa
00:15:23.258 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用使用体验,注册模型：设备类型
00:15:23.258 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用落尘管理,注册视图：落尘点位（视图）
00:15:23.265 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用能耗分析,注册接口：219f628dee3f10da276afac5d782cc8c
00:15:23.265 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用标准物质管理,注册视图：标准滴定溶液(BY)配置列表（主任务
00:15:23.265 [ForkJoinPool.commonPool-worker-12] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用hhh,注册视图：1516
00:15:23.266 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用配液管理,注册视图：配液管理
00:15:23.266 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用金属地图,注册视图：质量管理-金属地图-金属地图
00:15:23.267 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件销毁列表
00:15:23.267 [ForkJoinPool.commonPool-worker-62] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 过程检验 应用Id: 43aa2bba422b88bafa214cc22d909ead
00:15:23.267 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试外部条码,注册视图：测试条码
00:15:23.270 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 检验元素 应用Id: 2cef3de0a7ffb7ada9f4a2f3642d6238
00:15:23.273 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用异常处置单,注册视图：异常处置单
00:15:23.273 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原合同清单,注册视图：原合同清单
00:15:23.274 [ForkJoinPool.commonPool-worker-15] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 样品名称管理 应用Id: b4d167cb4e294f7ab17202dc7194c0f5
00:15:23.273 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用落尘管理,注册视图：落尘检测元素
00:15:23.284 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用标准物质管理,注册视图：子任务模板
00:15:23.284 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用车间,注册接口：6477e9602c7ee96c1f7bc4b521cb3582
00:15:23.284 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用配液管理,注册视图：配液管理
00:15:23.284 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 委托单位管理 应用Id: f5bff7a82812f248832defa7e17339fc
00:15:23.284 [ForkJoinPool.commonPool-worker-62] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用过程检验,注册视图：过程检验
00:15:23.284 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 周期模板 应用Id: e9e4091e262fdd3f05e14a7a53778750
00:15:23.285 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件台账表单
00:15:23.285 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 工序字典 应用Id: 2e4e141cb09288af99f9bced8a8608de
00:15:23.285 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试外部条码,注册视图：dsad
00:15:23.286 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用流程测试应用,注册接口：1f5789535bb55b6dce267e55712bba9f
00:15:23.287 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检验元素,注册视图：检测元素
00:15:23.287 [ForkJoinPool.commonPool-worker-15] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用样品名称管理,注册视图：样品名称管理列表
00:15:23.296 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用cdb,注册模型：模型测试
00:15:23.296 [ForkJoinPool.commonPool-worker-30] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备报修,注册视图：设备报修
00:15:23.296 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 测试业务 应用Id: 7be0519123db41ede8f4ce3673c204a7
00:15:23.297 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用委托单位管理,注册视图：委托单位管理列表
00:15:23.297 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用QQ,注册视图：FF
00:15:23.297 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 设备基础数据管理 应用Id: c96c3c62cd9859e34178547396e8ded8
00:15:23.297 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工序字典,注册视图：工序字典
00:15:23.297 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用周期模板,注册视图：周期模板
00:15:23.297 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型_wjtest,注册视图：33
00:15:23.298 [ForkJoinPool.commonPool-worker-62] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 成品检验 应用Id: 72d6f9edddcb93c6bd60750d271bd43a
00:15:23.298 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试历史记录,注册视图：c
00:15:23.298 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用配液管理,注册接口：7ae32f2be81043ebbe5dc4d29ad63d43
00:15:23.298 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用标准物质管理,注册视图：标准滴定溶液(BY)基础配置表单
00:15:23.298 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用测试外部条码,注册接口：1e51f4d954d647afa6e90fe9c9902b13
00:15:23.298 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用能耗分析,注册接口：69bc77ce586c60e908e2a6bac6fcb5ba
00:15:23.298 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用格林美服务,注册视图：锁定列表
00:15:23.299 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用化验岗位管理,注册视图：岗位人员管理视图
00:15:23.299 [ForkJoinPool.commonPool-worker-16] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用不合格处置单,注册模型：不合格处置单
00:15:23.299 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仓库设置,注册视图：仓库管理表单
00:15:23.300 [ForkJoinPool.commonPool-worker-15] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用样品名称管理,注册视图：样品名称管理表单
00:15:23.304 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试业务,注册视图：测试流程
00:15:23.305 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据管理,注册视图：设备类型表单
00:15:23.305 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工序字典,注册视图：工序字典表单
00:15:23.305 [ForkJoinPool.commonPool-worker-12] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用hhh,注册模型：oo
00:15:23.306 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用委托单位管理,注册视图：委托单位管理表单
00:15:23.306 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试历史记录,注册模型：asdzx
00:15:23.306 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用ADMIN新增业务,注册视图：测试编码生成2
00:15:23.306 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用周期模板,注册视图：周期模板表单
00:15:23.306 [ForkJoinPool.commonPool-worker-62] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用成品检验,注册视图：成品检验
00:15:23.307 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测依据管理,注册接口：ad81e54557a2c11e086838447a3514c3
00:15:23.307 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用车间,注册模型：车间
00:15:23.308 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用基础数据,注册接口：a9638f9cf4744c2d869efa8be6bacf8f
00:15:23.313 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试业务,注册视图：测试模型
00:15:23.314 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 样品类型管理 应用Id: eb9b2eea20b34875061a89b480acd891
00:15:23.315 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料类型,注册模型：物料类型表单
00:15:23.315 [ForkJoinPool.commonPool-worker-11] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 样品类型管理 应用Id: 23f5c8dd774c1823f8d749e5cbdef318
00:15:23.316 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用配液管理,注册接口：f9356c94838b48bab5509bacc0c062be
00:15:23.316 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用测试外部条码,注册接口：b17c9638ec834b7ca17f5fc692a17299
00:15:23.316 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 物料仓管理 应用Id: 9ece866d2cc676499c3a0bdaab503f59
00:15:23.316 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用流程测试应用,注册模型：测试流程
00:15:23.322 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用QQ,注册视图：执行节点
00:15:23.322 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用样品类型管理,注册视图：样品类型管理表格
00:15:23.323 [ForkJoinPool.commonPool-worker-23] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试,注册视图：fileModal
00:15:23.323 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型_wjtest,注册视图：44
00:15:23.323 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试历史记录,注册模型：qweaaaa
00:15:23.324 [ForkJoinPool.commonPool-worker-11] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用样品类型管理,注册视图：样品类型种类管理
00:15:23.324 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用检测依据管理,注册模型：检测依据表单
00:15:23.324 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料库存列表
00:15:23.328 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用统一表格搜索,注册视图：数据管理
00:15:23.329 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据,注册视图：作业流程管理表单
00:15:23.330 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用计算公式管理,注册接口：da02caf0cd195e3b93225fb383a6cdde
00:15:23.330 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用样品类型管理,注册视图：样品类型管理
00:15:23.330 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工序字典,注册模型：工序字典表单
00:15:23.330 [ForkJoinPool.commonPool-worker-11] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用样品类型管理,注册视图：样品类型种类管理
00:15:23.331 [ForkJoinPool.commonPool-worker-25] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用平台BUG,注册视图：BUG管理
00:15:23.331 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用配液管理,注册接口：be703dde0cdb4541867513dc2024aadc
00:15:23.331 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料预警列表
00:15:23.331 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 物料基础配置 应用Id: 925dfd5a99fe1c1dbd4f85a86f2eec20
00:15:23.337 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 测试字段 应用Id: c0f4ef168bbbed34db39ac257d1ce5f6
00:15:23.337 [ForkJoinPool.commonPool-worker-16] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 工艺路线配置 应用Id: 5484342dbdd201a2044a3ba6a4b62f48
00:15:23.338 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用统一表格搜索,注册模型：样品列表
00:15:23.339 [ForkJoinPool.commonPool-worker-54] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用客户需求管理,注册视图：客户需求管理（视图）
00:15:23.339 [ForkJoinPool.commonPool-worker-12] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用hhh,注册模型：1516
00:15:23.339 [ForkJoinPool.commonPool-worker-33] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用铜显色,注册视图：铜显色检测点位（视图）
00:15:23.340 [ForkJoinPool.commonPool-worker-25] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用平台BUG,注册模型：问题反馈
00:15:23.340 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 客户 应用Id: 4dff6ff27e22c634acf80c737d07cf4e
00:15:23.340 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 仓库管理 应用Id: 9f422241fde4b8ca0610c0aa03a987fd
00:15:23.343 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料信息列表
00:15:23.344 [ForkJoinPool.commonPool-worker-16] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺路线配置,注册视图：工艺流程主表
00:15:23.344 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用测试历史记录,注册流程：null
00:15:23.344 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 测试统一数据管理界面 应用Id: 7336b315b2ef9d43c0a9c706ccea39a7
00:15:23.344 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试,注册视图：物料类型
00:15:23.344 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据管理,注册视图：设备类型
00:15:23.345 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工序管理,注册视图：工序管理
00:15:23.346 [ForkJoinPool.commonPool-worker-54] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用客户需求管理,注册视图：客户需求看板
00:15:23.346 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用计算公式管理,注册接口：543649de57ab79a76616523964e8c455
00:15:23.346 [ForkJoinPool.commonPool-worker-33] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用铜显色,注册视图：铜显色检测执行
00:15:23.346 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用寄样管理,注册视图：寄样管理（视图）
00:15:23.346 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 设备管理 应用Id: bad08ffe5cc18753adea70e1cc087d6b
00:15:23.347 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用客户,注册视图：客户视图管理
00:15:23.351 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 0417 应用Id: c8b898ae59781a7f9996caea5eeb89e5
00:15:23.351 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：库存日志列表
00:15:23.351 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试统一数据管理界面,注册视图：w
00:15:23.352 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用配液管理,注册模型：产品规格配置
00:15:23.352 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试,注册视图：test
00:15:23.352 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据管理,注册视图：设备区域表单
00:15:23.352 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用格林美服务,注册视图：采购单列表
00:15:23.353 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原合同清单,注册视图：一个新的数据模型
00:15:23.353 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用基础数据,注册接口：82099b1df06846bf877b594166314f46
00:15:23.353 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工序管理,注册模型：工序
00:15:23.353 [ForkJoinPool.commonPool-worker-54] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用客户需求管理,注册接口：1e9afb0a3dc84c4ab165c31abab16b52
00:15:23.353 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用金属地图,注册视图：质量管理-金属地图-金属地图设备
00:15:23.353 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备管理,注册视图：设备申购列表
00:15:23.353 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用寄样管理,注册接口：5fc39ce256db45efb661955a19719923
00:15:23.354 [ForkJoinPool.commonPool-worker-12] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 测试 应用Id: 7a39900cf163f0313bbb63ff62537c94
00:15:23.354 [ForkJoinPool.commonPool-worker-11] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用样品类型管理,注册视图：样品类型表单
00:15:23.354 [ForkJoinPool.commonPool-worker-33] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用铜显色,注册视图：铜显色记录
00:15:23.354 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用客户,注册视图：客户
00:15:23.357 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用留样仓管理,注册接口：85e56e6953da8314c239377f9f1aa342
00:15:23.358 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试统一数据管理界面,注册视图：s
00:15:23.358 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料类型列表
00:15:23.358 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用QQ,注册视图：11
00:15:23.359 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用周期模板,注册接口：0ca61331ef063ff6351a09995b1f4118
00:15:23.359 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型_wjtest,注册视图：55
00:15:23.359 [ForkJoinPool.commonPool-worker-23] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试,注册视图：测试编码
00:15:23.359 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用计算公式管理,注册接口：80c664c87b6600737495722fd1050982
00:15:23.359 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备管理,注册视图：设备台账列表
00:15:23.360 [ForkJoinPool.commonPool-worker-12] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: ADMIN添加测试 应用Id: 9fd9feaca1db8f6ddfef0fb668572b6a
00:15:23.364 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试统一数据管理界面,注册视图：统一测试表格
00:15:23.364 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用仓库设置,注册模型：货架管理表单
00:15:23.364 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试外部条码,注册模型：测试条码
00:15:23.364 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：出库记录列表
00:15:23.365 [ForkJoinPool.commonPool-worker-23] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: ggg 应用Id: e6b47c8c70612d77eceac2b07ef03ab4
00:15:23.365 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 物料基础信息 应用Id: 085c1c4ff6bebecd838d123a35e3fccd
00:15:23.366 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备管理,注册视图：检定计划列表
00:15:23.364 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用标准物质管理,注册视图：基准物质配置表单(基础配置)
00:15:23.368 [ForkJoinPool.commonPool-worker-12] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用ADMIN添加测试,注册视图：测试
00:15:23.373 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料管理,注册视图：物料数据管理界面
00:15:23.373 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用ADMIN新增业务,注册视图：cq
00:15:23.373 [ForkJoinPool.commonPool-worker-25] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用平台BUG,注册流程：null
00:15:23.373 [ForkJoinPool.commonPool-worker-15] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用样品名称管理,注册接口：45fc22a1f92dd0ba32aa91226f97a3ce
00:15:23.374 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用商务合同,注册视图：查看界面
00:15:23.375 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料盘点列表
00:15:23.375 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用计算公式管理,注册模型：计算公式管理表单
00:15:23.375 [ForkJoinPool.commonPool-worker-23] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用ggg,注册视图：qwe
00:15:23.375 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料基础信息,注册视图：物料类型视图
00:15:23.376 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备管理,注册视图：设备检定列表
00:15:23.376 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用留样仓管理,注册接口：049802676ecb1131809bd9d21c4968b1
00:15:23.376 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用周期模板,注册接口：fab65acd0515176d96ad3283a677d9a6
00:15:23.376 [ForkJoinPool.commonPool-worker-12] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用ADMIN添加测试,注册视图：dasda
00:15:23.381 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试业务,注册视图：测试流程
00:15:23.381 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料管理,注册模型：物料信息
00:15:23.381 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用流程测试,注册模型：wwwwwwwwwwwww
00:15:23.382 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用QQ,注册视图：qwd
00:15:23.383 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：入库记录列表
00:15:23.384 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型_wjtest,注册视图：66
00:15:23.384 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用仓库设置,注册模型：仓库管理表单
00:15:23.384 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料基础信息,注册视图：物料管理视图
00:15:23.385 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用测试外部条码,注册流程：null
00:15:23.385 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 退货检验 应用Id: 5335ab9fb9f222f3adf4ff9f27c65d84
00:15:23.385 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备管理,注册视图：核查计划列表
00:15:23.385 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用统一表格搜索,注册模型：方法依据列表(点击方法依据名称可进行选择)
00:15:23.388 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检验元素,注册视图：检测元素（视图）
00:15:23.389 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型_wj,注册视图：工艺模型审批
00:15:23.389 [ForkJoinPool.commonPool-worker-15] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用样品名称管理,注册模型：样品名称管理表单
00:15:23.389 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料信息表单
00:15:23.390 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用留样仓管理,注册模型：入库样品列表
00:15:23.390 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料基础信息,注册视图：物料类型
00:15:23.390 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备管理,注册视图：期间核查列表
00:15:23.390 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用退货检验,注册视图：退货检验
00:15:23.390 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用周期模板,注册模型：周期模板表单
00:15:23.390 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原合同清单,注册视图：rty
00:15:23.391 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 简单审批流 应用Id: 21722f7b28caa6ad09690021200b9b83
00:15:23.396 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据管理,注册视图：设备区域
00:15:23.396 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用ADMIN新增业务,注册接口：915e4c8a331a733c0732fa9e4fdba451
00:15:23.396 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用样品类型管理,注册接口：eddd1d3ab6fc4ac81362dcf545d7764a
00:15:23.396 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用流程测试,注册模型：asd
00:15:23.397 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用检验元素,注册模型：检测元素
00:15:23.397 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用寄样管理,注册接口：17ad7a3c4ec04f6eae68a2a9fcef3c4e
00:15:23.397 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用异常处置单,注册模型：异常处置单
00:15:23.397 [ForkJoinPool.commonPool-worker-54] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用客户需求管理,注册模型：客户需求管理
00:15:23.398 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原合同清单,注册视图：列表
00:15:23.398 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 货架管理 应用Id: 86b3167eaf2795612819ef26bf6a71b0
00:15:23.399 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备管理,注册视图：巡检保修列表
00:15:23.399 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 巡检管理 应用Id: 1276eb7a744b1cc82030b2f9d276b5bb
00:15:23.399 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 样品类型管理 应用Id: f1983436253625c6a167d411fc2721e1
00:15:23.400 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用简单审批流,注册视图：简易流程提交界面
00:15:23.400 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用测试外部条码,注册触发器：ttt
00:15:23.403 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用基础数据,注册接口：4f03f3fd4dd14b18af8e535882bc07ea
00:15:23.404 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据管理,注册视图：厂家信息表单
00:15:23.404 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型-lyh,注册视图：工艺模型流程视图
00:15:23.405 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [lambda$register$9,223] - 注册应用测试外部条码,注册条码：自定义条码
00:15:23.405 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用格林美服务,注册视图：订单列表
00:15:23.405 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用货架管理,注册视图：货架管理表单
00:15:23.405 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型_wjtest,注册视图：77
00:15:23.405 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用巡检管理,注册视图：巡检项目表
00:15:23.405 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备管理,注册视图：设备报废列表
00:15:23.406 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原合同清单,注册视图：UI设计
00:15:23.406 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 设备台账 应用Id: 9f2e7519dfec2e6d7e46437ffd2ad263
00:15:23.405 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用样品类型管理,注册视图：样品类型管理列表
00:15:23.407 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用简单审批流,注册视图：简易流程提交界面
00:15:23.408 [ForkJoinPool.commonPool-worker-15] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: ff 应用Id: 2cb180aa96d6002fead2013473ec6389
00:15:23.411 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用ADMIN新增业务,注册接口：11fd1b68380d3c1f7cb2defd68448790
00:15:23.411 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用统一表格搜索,注册模型：加密换袋列表
00:15:23.412 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用样品类型管理,注册模型：样品类型管理
00:15:23.412 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 11 应用Id: 014a236d1de8a8ef83735885ac729bc3
00:15:23.412 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 条码 应用Id: 3a9336b4ab0434499952044bb2047f8f
00:15:23.412 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型_wjtest,注册视图：88
00:15:23.412 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 物料类型 应用Id: 05ac901f070b854c5484e9095113aa8a
00:15:23.412 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据,注册视图：计量设备管理表单
00:15:23.412 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用留样仓管理,注册模型：样品入库管理表单
00:15:23.413 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用样品类型管理,注册视图：样品类型管理表单
00:15:23.413 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备管理,注册视图：检定计划表单
00:15:23.413 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备台账,注册视图：设备台账表单
00:15:23.413 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用异常处置单,注册流程：null
00:15:23.418 [ForkJoinPool.commonPool-worker-42] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用费用配置,注册视图：价格方案列表
00:15:23.418 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试统一数据管理界面,注册模型：统一测试表格
00:15:23.419 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用基础数据,注册接口：85570800fe2847f29ede316efcd007a7
00:15:23.419 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 单位管理 应用Id: 9a1fcefdb2c0eed1150b736fa6c29296
00:15:23.419 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用条码,注册视图：打印测试
00:15:23.419 [ForkJoinPool.commonPool-worker-25] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 客户标准维护 应用Id: 3782ad7c005257b9a6e21bd49e843ec5
00:15:23.420 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型_wjtest,注册视图：99
00:15:23.420 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用标准物质管理,注册视图：标定公式配置表单
00:15:23.420 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料类型,注册视图：物料类型
00:15:23.421 [ForkJoinPool.commonPool-worker-11] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用样品类型管理,注册模型：样品类型表单
00:15:23.423 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用质控样管理,注册接口：6c4c47bb4b5b725623ec0c3570b2626c
00:15:23.424 [ForkJoinPool.commonPool-worker-42] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用费用配置,注册视图：计划用量
00:15:23.424 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用单位管理,注册视图：单位视图管理
00:15:23.424 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用条码,注册视图：jj
00:15:23.425 [ForkJoinPool.commonPool-worker-25] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用客户标准维护,注册视图：客户标准维护
00:15:23.426 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型_wjtest,注册视图：100
00:15:23.426 [ForkJoinPool.commonPool-worker-12] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用ADMIN添加测试,注册视图：测试视图
00:15:23.426 [ForkJoinPool.commonPool-worker-54] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 工艺配置 应用Id: 10e0dd6530b170363d7104b53afa80d1
00:15:23.430 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用客户,注册接口：6477e9602c7ee96c1f7bc4b521cb3582
00:15:23.430 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用金属地图,注册视图：金属地图（视图）
00:15:23.430 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用条码,注册视图：打印打印
00:15:23.429 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用测试业务,注册接口：f87f0168bfcb514438a2f890bcf5bad0
00:15:23.430 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用基础数据,注册接口：eeef1a878d7f41898c0bea4509d65d74
00:15:23.430 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用单位管理,注册视图：单位
00:15:23.431 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型_wjtest,注册视图：101
00:15:23.432 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 除磁管理 应用Id: 8a99d8d976ab15d01746768076d86b6e
00:15:23.432 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用QQ,注册视图：子流程
00:15:23.432 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用统一表格搜索,注册模型：收样列表
00:15:23.433 [ForkJoinPool.commonPool-worker-54] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 单位管理 应用Id: 0c22bf82875a002a20c6050b94df42e8
00:15:23.433 [ForkJoinPool.commonPool-worker-12] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用ADMIN添加测试,注册模型：dasda
00:15:23.434 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用化验岗位管理,注册接口：547f5b997f6e47a97aab908b482cd7e3
00:15:23.435 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用质控样管理,注册接口：beaabc79937eb26f7a38b03974967d2a
00:15:23.435 [ForkJoinPool.commonPool-worker-11] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 项目 应用Id: a702150952dffcfa6b96cb40d60b730a
00:15:23.435 [ForkJoinPool.commonPool-worker-23] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用ggg,注册模型：qwe
00:15:23.435 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用条码,注册视图：tttt
00:15:23.435 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用金属地图,注册视图：金属地图查询视图
00:15:23.436 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用寄样管理,注册模型：寄样管理
00:15:23.438 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用除磁管理,注册视图：除磁点位
00:15:23.438 [ForkJoinPool.commonPool-worker-54] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用单位管理,注册视图：单位
00:15:23.439 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用落尘管理,注册视图：落尘检测元素（视图）
00:15:23.439 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 检测流程 应用Id: 84e28a21d03aee9a8ddaa0341d06a389
00:15:23.440 [ForkJoinPool.commonPool-worker-11] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用项目,注册视图：项目管理界面
00:15:23.440 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试,注册视图：test
00:15:23.440 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用条码,注册视图：条码测试模型
00:15:23.440 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用客户,注册模型：客户
00:15:23.440 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试业务,注册模型：data-table
00:15:23.443 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用基础数据,注册接口：fe37ab1c274d4b339783ba8b39792b8d
00:15:23.443 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用格林美服务,注册视图：采购单列表
00:15:23.443 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报告流程管理,注册接口：548f84eaee0efce1c404d9547b25c877
00:15:23.443 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料类型,注册视图：物料类型管理
00:15:23.444 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检测流程,注册视图：待认领任务视图
00:15:23.444 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料基础信息,注册视图：物料料管理
00:15:23.444 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用货架管理,注册视图：货架管理列表
00:15:23.444 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用落尘管理,注册视图：落尘取样
00:15:23.445 [ForkJoinPool.commonPool-worker-23] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 测试流程-生产通知 应用Id: 85f7d7f7963a1a0e6215b3ad0a8ad495
00:15:23.444 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用化验岗位管理,注册接口：941357f14575709a54851ced33754f08
00:15:23.444 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用质控样管理,注册接口：f54c2461260a915a3840e3b9c1cb5ce3
00:15:23.444 [ForkJoinPool.commonPool-worker-11] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用项目,注册视图：项目
00:15:23.444 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料库存表单
00:15:23.444 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试,注册视图：文件测试
00:15:23.448 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用寄样管理,注册流程：null
00:15:23.449 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用统一表格搜索,注册模型：表格搜索
00:15:23.449 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用格林美服务,注册视图：订单列表
00:15:23.449 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料类型,注册模型：物料类型
00:15:23.449 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托单位管理,注册接口：d7b7d07b2b010188d45dba830b031e87
00:15:23.450 [ForkJoinPool.commonPool-worker-23] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试流程-生产通知,注册视图：数据管理界面
00:15:23.450 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用货架管理,注册模型：货架管理表单
00:15:23.452 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用格林美服务,注册视图：库存列表
00:15:23.453 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报告流程管理,注册接口：41bb0fba880e7ff07d5512252acacdc2
00:15:23.454 [ForkJoinPool.commonPool-worker-23] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试流程-生产通知,注册视图：测试发起
00:15:23.456 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用化验岗位管理,注册接口：21003387e6289d1d1dc12012694ca229
00:15:23.456 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用QQ,注册接口：bbff5394122409728798e8f7a363ce8a
00:15:23.456 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用质控样管理,注册接口：f2860c678351f74ddfbe09252aacd714
00:15:23.457 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用格林美服务,注册接口：a2492ad6429e0829d64ef788eb146a59
00:15:23.457 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用统一表格搜索,注册流程：null
00:15:23.457 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用委托单位管理,注册模型：委托单位管理表单
00:15:23.458 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 客户投诉管理 应用Id: ed33df5703cbefcde3827d936bf20f6d
00:15:23.458 [ForkJoinPool.commonPool-worker-23] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试流程-生产通知,注册视图：生产通知
00:15:23.458 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 人员管理 应用Id: b913c0f9decae3610cbfb43288abc782
00:15:23.460 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用样品类型管理,注册模型：样品类型管理表单
00:15:23.460 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 产品类型 应用Id: 318da15175cc3b881b0ec34b25461e57
00:15:23.460 [ForkJoinPool.commonPool-worker-33] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用铜显色,注册接口：d2fd8cf07f3a490f86ffdb89af29945c
00:15:23.461 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用简单审批流,注册接口：fa49c1af452dc642856e5bb683cbe891
00:15:23.461 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用单位管理,注册模型：单位
00:15:23.461 [ForkJoinPool.commonPool-worker-30] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备报修,注册接口：c74cf2cda20beeb0904950c68e6d15e7
00:15:23.461 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用客户投诉管理,注册视图：客户投诉管理
00:15:23.461 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用人员管理,注册视图：人员能力确认列表
00:15:23.462 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型_wjtest,注册视图：102
00:15:23.463 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试业务,注册模型：测试模型
00:15:23.463 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用产品类型,注册视图：产品类型管理
00:15:23.464 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用化验岗位管理,注册接口：30ba4d098dd99481f0edfd357cc157ab
00:15:23.464 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用QQ,注册接口：95fd1bb19b50bc407a01f639892de4da
00:15:23.464 [ForkJoinPool.commonPool-worker-16] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺路线配置,注册接口：b890e6a4e1b545c289394460dd0d4fdc
00:15:23.465 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 样品检测管理 应用Id: 482c2acd0719ad77ec25a8f1ed93e3f6
00:15:23.465 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报告流程管理,注册接口：1c9890710c074c440eced647d5fa9f1b
00:15:23.465 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用质控样管理,注册接口：81a7114a44ac306e913ce930c0ef9859
00:15:23.465 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 测试引用应用 应用Id: 6aad07eae6d58b305af55371aceed9e2
00:15:23.465 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用人员管理,注册视图：人员岗位培训列表
00:15:23.467 [ForkJoinPool.commonPool-worker-11] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用项目,注册接口：34d22a1e17d96acfe9f324c8bbf351fa
00:15:23.468 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用产品类型,注册视图：产品类型
00:15:23.468 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用基础数据,注册接口：97741a02635648d39536a34496e6dfcf
00:15:23.468 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用条码,注册视图：uj
00:15:23.468 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 测试模版--0914 应用Id: cc072c957abd742e62446281d215bf6a
00:15:23.469 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用商务合同,注册模型：商务合同模型
00:15:23.469 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型_wj,注册视图：工艺模型审批视图
00:15:23.469 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据管理,注册视图：厂家信息
00:15:23.469 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用样品检测管理,注册视图：样品检测管理列表
00:15:23.469 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试引用应用,注册视图：popo
00:15:23.472 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用简单审批流,注册模型：简易流程提交界面
00:15:23.472 [ForkJoinPool.commonPool-worker-30] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备报修,注册模型：故障等级
00:15:23.472 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用人员管理,注册视图：人员基本信息列表
00:15:23.473 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试模版--0914,注册视图：数据模型
00:15:23.473 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型_wj,注册接口：c677ddfae0c844b8b7fd6e073d937db5
00:15:23.473 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用QQ,注册接口：531f42ba9692c0980d094fd1a80eb122
00:15:23.473 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据管理,注册视图：产线表单
00:15:23.473 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报告流程管理,注册接口：c94f5c5fc6a3c2dffca9b25265dc7ae8
00:15:23.473 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件柜号表单
00:15:23.474 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用样品检测管理,注册视图：样品检测管理表单
00:15:23.474 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用质控样管理,注册接口：babd01e50835ab0b8853f179d66b80e3
00:15:23.473 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用化验岗位管理,注册接口：4ccb792e08e1b1d59f62a33e0c561d5c
00:15:23.475 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备管理,注册视图：核查计划表单
00:15:23.475 [ForkJoinPool.commonPool-worker-11] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用项目,注册模型：项目
00:15:23.475 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型-lyh,注册视图：工艺模型流程数据视图
00:15:23.475 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 设备维修 应用Id: 8023119b0a2bc72a524847b4ba4e542d
00:15:23.476 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用人员管理,注册视图：人员能力确认表单
00:15:23.476 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用金属地图,注册视图：金属地图设备（视图）
00:15:23.478 [ForkJoinPool.commonPool-worker-30] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备报修,注册模型：设备报修类型
00:15:23.479 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备维修,注册视图：设备维修列表
00:15:23.480 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用QQ,注册接口：ab2a0d922917d9e1f6cf7a0ac6c1780c
00:15:23.480 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报告流程管理,注册接口：bcc01b33536d0e1c96895c1a8380c79b
00:15:23.480 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：66296ba5f5bb4f4dbb14146ae976e120
00:15:23.482 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用金属地图,注册视图：修改设备版本
00:15:23.482 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型_wj,注册接口：bb4232d11c4141b5a47cb7db5a7ecdcf
00:15:23.482 [ForkJoinPool.commonPool-worker-25] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用客户标准维护,注册视图：客户标准维护(视图)
00:15:23.482 [ForkJoinPool.commonPool-worker-54] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用单位管理,注册视图：单位数据管理
00:15:23.482 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 项目类别管理 应用Id: 289a18b37e7ec649346d252f331a38a2
00:15:23.483 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试业务,注册模型：测试流程
00:15:23.483 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用质控样管理,注册模型：定值列表
00:15:23.483 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用化验岗位管理,注册接口：0664cdd045dcd6f45651004b015cb483
00:15:23.483 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试,注册视图：文件类型
00:15:23.484 [ForkJoinPool.commonPool-worker-33] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用铜显色,注册接口：a650437d3937459e95cbe7e2c8a0c570
00:15:23.484 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备维修,注册视图：委外管理列表
00:15:23.484 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据管理,注册视图：产线
00:15:23.484 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件柜位表单
00:15:23.485 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备报废,注册接口：18911aca1a5230029303a5d31002d24c
00:15:23.485 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试引用应用,注册视图：sss
00:15:23.485 [ForkJoinPool.commonPool-worker-42] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用费用配置,注册模型：价格方案
00:15:23.485 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：d910a1eadaa705b0db63fc637bb0f37a
00:15:23.485 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 设备检定 应用Id: 103934abaa5515e470ef807130263d63
00:15:23.485 [ForkJoinPool.commonPool-worker-23] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试流程-生产通知,注册模型：生产通知
00:15:23.485 [ForkJoinPool.commonPool-worker-16] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺路线配置,注册接口：b8253e9df28f404695de2c2f8ac7f775
00:15:23.485 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用条码,注册视图：dddd
00:15:23.485 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用项目类别管理,注册视图：项目类别列表
00:15:23.485 [ForkJoinPool.commonPool-worker-54] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用单位管理,注册接口：ef95eb32f0044e18bd6a91ecf8d8b7e5
00:15:23.485 [ForkJoinPool.commonPool-worker-25] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用客户标准维护,注册接口：5c501a6e654b4ab28257c0e71f15535c
00:15:23.485 [ForkJoinPool.commonPool-worker-30] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备报修,注册模型：设备报修
00:15:23.487 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备维修,注册视图：test
00:15:23.487 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用QQ,注册接口：631784a140efdb8bd377ad7fbed36a3b
00:15:23.487 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据管理,注册视图：模板名称表单
00:15:23.487 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用产品类型,注册模型：产品类型
00:15:23.487 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报告流程管理,注册接口：b062b7d7c159bbc3aebc1a4dc627e844
00:15:23.487 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：84a497b9bedc4d529ef23d133c3a8180
00:15:23.487 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备检定,注册视图：设备检定列表
00:15:23.487 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试引用应用,注册模型：popo
00:15:23.488 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用项目类别管理,注册视图：项目类别管理表单
00:15:23.488 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试模版--0914,注册视图：统一数据管理视图
00:15:23.488 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用测试业务,注册流程：null
00:15:23.488 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用基础数据,注册接口：f429dc544f5e4ef8a902536d22a7039f
00:15:23.489 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型_wj,注册接口：9da7761a854f44619770da734b9fe798
00:15:23.490 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用化验岗位管理,注册接口：97b287128a8549429646c8b24a729b8a
00:15:23.491 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用质控样管理,注册模型：定值列表
00:15:23.491 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备检定,注册视图：检定计划列表
00:15:23.491 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试模版--0914,注册视图：业务模型
00:15:23.491 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备报废,注册模型：设备报废表单
00:15:23.500 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报告流程管理,注册接口：7e41ffb6fad935e17547eb073018475a
00:15:23.500 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：8e46ef997cfb458d8eff9b7cde434205
00:15:23.500 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用条码,注册视图：条码测试流程
00:15:23.500 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测方法管理,注册接口：6da4ec1780e05111d55e7ebbc2ee9744
00:15:23.500 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 低码 应用Id: fa8049d18062215b6fe70c3dd25726a4
00:15:23.500 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备检定,注册视图：检定计划表单
00:15:23.500 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用除磁管理,注册视图：除磁点位（视图）
00:15:23.499 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用QQ,注册接口：1bb76812729d4d7d9186ca75acf7b424
00:15:23.502 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用巡检管理,注册视图：巡检记录
00:15:23.502 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型_wj,注册接口：df51b970fd8d4fa2882f1145bc8cff98
00:15:23.502 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检测流程,注册视图：待任务认领表单
00:15:23.502 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用化验岗位管理,注册接口：62804de51a39e15134af721311ca47aa
00:15:23.503 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试,注册视图：文件类型
00:15:23.504 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用除磁管理,注册视图：除磁执行
00:15:23.504 [ForkJoinPool.commonPool-worker-30] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: cdb 应用Id: 893d25080ffbb6820b78009dabc2b2eb
00:15:23.504 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用测试业务,注册触发器：流程结束
00:15:23.504 [ForkJoinPool.commonPool-worker-42] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用费用配置,注册模型：计划用量
00:15:23.505 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 用来测试的 应用Id: 1e7fe90ba493e543740fa45825843aba
00:15:23.505 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件入库表单
00:15:23.505 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料预警表单
00:15:23.505 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用落尘管理,注册视图：落尘趋势分析
00:15:23.506 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试,注册视图：文件测试
00:15:23.507 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：5ba703491e5948cd859248edbfab1b24
00:15:23.507 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测方法管理,注册接口：30355febff91564e59cd0e8dfec58f7a
00:15:23.507 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报告流程管理,注册接口：e722c5ed5d7fede63a50bb835870d390
00:15:23.507 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试模版--0914,注册模型：数据模型
00:15:23.507 [ForkJoinPool.commonPool-worker-33] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用铜显色,注册模型：铜显色检测点位
00:15:23.507 [ForkJoinPool.commonPool-worker-30] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: cdb2 应用Id: ed404e7a92e227dc613f4b511a9cc18c
00:15:23.507 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用除磁管理,注册接口：0028421972464d3e9216233997e6ceab
00:15:23.507 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：简单的流程
00:15:23.507 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用质控样管理,注册模型：质控样表单
00:15:23.507 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备维修,注册视图：设备维修
00:15:23.508 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用化验岗位管理,注册模型：化验岗位表单
00:15:23.508 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型_wj,注册接口：94645bf175ac4b4b9f9c918d17f4a440
00:15:23.508 [ForkJoinPool.commonPool-worker-25] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用客户标准维护,注册接口：7b8164deaf074020a7898b1b1b6416d3
00:15:23.509 [ForkJoinPool.commonPool-worker-54] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用单位管理,注册接口：16aad1005efa46839474b4b64b95ad24
00:15:23.509 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试,注册模型：物料类型
00:15:23.509 [ForkJoinPool.commonPool-worker-16] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺路线配置,注册接口：c8cc11ea14694dcabdf6ea8514ddb0e1
00:15:23.509 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：测试模型管理
00:15:23.509 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型_wjtest,注册接口：dc9e5c7918274e4c8a5218ce2b7f6c7a
00:15:23.512 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用基础数据,注册接口：1d1f4d21dad943b9a923cecbf7b34d9e
00:15:23.531 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 委托流程 应用Id: 082d9790fc41f7d03bb533fefe1de9e8
00:15:23.512 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：8f8ac855c5a14465a6cbc561ce3f1b42
00:15:23.532 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试模版--0914,注册模型：业务模型
00:15:23.532 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报告流程管理,注册接口：08736fcb8aad692da9931ab66c68131c
00:15:23.532 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用标准物质管理,注册视图：标准物质台账表单
00:15:23.512 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测方法管理,注册接口：b4ee1eb1e62514a8739098b91ea4c638
00:15:23.534 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 仪表配置 应用Id: 762fa33439428e386b046293925ee5d1
00:15:23.534 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据管理,注册视图：模板名称
00:15:23.534 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：测试模型
00:15:23.534 [ForkJoinPool.commonPool-worker-33] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用铜显色,注册模型：data-table
00:15:23.534 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用项目类别管理,注册模型：项目类别管理表单
00:15:23.534 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型_wj,注册接口：43b2fb2ba3f144bc9cbf57d340e4b3f1
00:15:23.534 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用委托流程,注册视图：样品委托流程表单
00:15:23.534 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 测试0507 应用Id: 302c665e86b6debc31f8cd0a31ff6559
00:15:23.536 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试,注册模型：test
00:15:23.536 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用QQ,注册接口：9bec1c642e4f49829084db22b1079974
00:15:23.536 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据,注册视图：自检计划表单
00:15:23.536 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据管理,注册视图：巡检表单
00:15:23.537 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 仪表配置 应用Id: bfc6443a7d7e893aa79ea2d4d57f679e
00:15:23.537 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用测试模版--0914,注册流程：null
00:15:23.537 [ForkJoinPool.commonPool-worker-42] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 抄表管理 应用Id: 6bd9f97a79ebfc7b779f5055fef0539f
00:15:23.538 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试0507,注册视图：列表1
00:15:23.538 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备管理,注册视图：设备申购表单
00:15:23.538 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：0ab666b3a6754830b7e9a621487f0aa0
00:15:23.539 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用报告流程管理,注册模型：检测依据
00:15:23.539 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测方法管理,注册接口：2b53093e7842bf3d2d9e93cbfb8ad3a2
00:15:23.540 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用落尘管理,注册视图：落尘检验单关联取样信息表
00:15:23.540 [ForkJoinPool.commonPool-worker-42] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用抄表管理,注册视图：用电抄表数据
00:15:23.540 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用条码,注册接口：b1adc9a97b8f03da543018601e1f45f6
00:15:23.540 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试0507,注册视图：测试1
00:15:23.541 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备台账,注册视图：设备台账
00:15:23.540 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仪表配置,注册视图：仪表供应商
00:15:23.541 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试,注册模型：文件测试
00:15:23.541 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型_wj,注册接口：7edf824866e743ec82cda023ed4e1a09
00:15:23.543 [ForkJoinPool.commonPool-worker-33] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用铜显色,注册模型：铜显色记录
00:15:23.543 [ForkJoinPool.commonPool-worker-25] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用客户标准维护,注册模型：检测元素表
00:15:23.543 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测方法管理,注册接口：28b5408071cf21458847bb619ad8ef94
00:15:23.543 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备台账,注册视图：二级设备表单
00:15:23.543 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：0e76b035fa2e41e5b77fcb3c03b70d41
00:15:23.544 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用除磁管理,注册模型：除磁点位
00:15:23.545 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料基础信息,注册接口：653bffbadc804e92816f137f83eaca4d
00:15:23.545 [ForkJoinPool.commonPool-worker-54] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用单位管理,注册模型：单位
00:15:23.545 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用条码,注册模型：tttt
00:15:23.546 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用金属地图,注册接口：d733dc08b28e4a07b7ad51f4cb6427ac
00:15:23.546 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型_wjtest,注册接口：b9d20bada406419eb190e6f8f41394c6
00:15:23.546 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用人员管理,注册视图：人员基本信息表单
00:15:23.547 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试,注册模型：文件类型
00:15:23.547 [ForkJoinPool.commonPool-worker-16] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺路线配置,注册接口：98e612c02f1540bd80b44279511237e1
00:15:23.547 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用报告流程管理,注册模型：报告详情
00:15:23.547 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用客户投诉管理,注册视图：原因分析模型
00:15:23.547 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用基础数据,注册接口：bfd87c20f9c34a94b3be1f240d0899a1
00:15:23.548 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型_wj,注册接口：0926c3f8556643b2be3740ab0001ad37
00:15:23.548 [ForkJoinPool.commonPool-worker-33] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 工序基础信息 应用Id: 2eea28c1d1b277753086a9fdd979b48b
00:15:23.549 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：11a9dc535d0a4f999b2c3033e00b8deb
00:15:23.550 [ForkJoinPool.commonPool-worker-42] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用抄表管理,注册接口：8b739f872509ee6e523d29067611891c
00:15:23.550 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用条码,注册模型：条码测试模型
00:15:23.550 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 入场物资 应用Id: ce2bb83641ede97a3f487ee8b4252ee8
00:15:23.550 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测方法管理,注册接口：8e85f40cf2874a2faf96054e0ff30184
00:15:23.550 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用QQ,注册模型：原始记录模版1
00:15:23.550 [ForkJoinPool.commonPool-worker-33] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工序基础信息,注册视图：t_base_process
00:15:23.550 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用客户投诉管理,注册视图：客户投诉管理（视图）
00:15:23.550 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料基础信息,注册接口：406fd8b1f9994eb282db056adaea5430
00:15:23.551 [ForkJoinPool.commonPool-worker-25] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用客户标准维护,注册模型：客户标准维护
00:15:23.551 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 公共接口工具 应用Id: 69413609edd059629c8e867229ced16d
00:15:23.551 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用入场物资,注册视图：入场物资记录表
00:15:23.553 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用公共接口工具,注册接口：9d9c2fb4ec97d68ad906942c56884878
00:15:23.553 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用客户投诉管理,注册视图：客户投诉数据展示(大屏)
00:15:23.553 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：1b427802b25e4a5485bf9b4ea8d2cfe8
00:15:23.553 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型_wj,注册接口：e415880a7467475d8455d8c1232162f4
00:15:23.553 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：库存日志表单
00:15:23.553 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用条码,注册模型：uj
00:15:23.554 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用QQ,注册模型：FF
00:15:23.555 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用检测方法管理,注册模型：检测项目配置
00:15:23.555 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备检定,注册视图：设备检定表单
00:15:23.555 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：用户列表
00:15:23.555 [ForkJoinPool.commonPool-worker-42] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 报告撤换 应用Id: a646a8d3e53f2d0d1ed98594ff5e855f
00:15:23.556 [ForkJoinPool.commonPool-worker-25] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 包辅材检验 应用Id: ae623c4eb42d14f8c40bf677ba4da3fd
00:15:23.556 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用报告流程管理,注册模型：报告流程管理表单
00:15:23.556 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料基础信息,注册接口：fc270c745908448cb596485ec918f2db
00:15:23.557 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用条码,注册模型：条码测试流程
00:15:23.558 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：简单的流程
00:15:23.558 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：d0f71a7f898f4451b7a888fece6c1259
00:15:23.558 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用公共接口工具,注册接口：5a917b810115f38198c64f68fa73776c
00:15:23.558 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用QQ,注册模型：执行节点
00:15:23.559 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试0507,注册模型：测试1
00:15:23.559 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用金属地图,注册接口：c5b54b8d956e4a89874ad4ac06960791
00:15:23.559 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用基础数据,注册接口：cf9ca8d59d4148759234f30f2516fcd5
00:15:23.559 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用客户投诉管理,注册接口：8fb38d1865ba43628821e73dfe31c56b
00:15:23.559 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型_wj,注册接口：128c55d1f2ff493eb9817fd9ee0080be
00:15:23.560 [ForkJoinPool.commonPool-worker-16] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺路线配置,注册接口：366a84472ac44c4d8a86c96e828ecd79
00:15:23.561 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仪表配置,注册视图：仪表供应商列表
00:15:23.561 [ForkJoinPool.commonPool-worker-33] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工序基础信息,注册视图：工序基础配置界面
00:15:23.561 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型_wjtest,注册接口：ebebdb90ab7643c2876bd57ebd782866
00:15:23.561 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用检测方法管理,注册模型：检测方法管理表单
00:15:23.561 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据,注册视图：添加计划
00:15:23.561 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料基础信息,注册接口：e988170a4ac34daca35fbef77563b429
00:15:23.563 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用QQ,注册模型：qwd
00:15:23.564 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 用户管理 应用Id: f919cb2ac3799ec8928c11afe5f10c65
00:15:23.563 [ForkJoinPool.commonPool-worker-33] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工序基础信息,注册模型：t_base_process
00:15:23.564 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仪表配置,注册视图：仪表区域
00:15:23.564 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：91fffc71054f4d70abe06c47d6033be4
00:15:23.563 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用公共接口工具,注册接口：d49e66fac67858359b67094ddf3afd5f
00:15:23.565 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用样品检测管理,注册接口：b9608e9a4fb68ac4908f53dacb2120b5
00:15:23.565 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型_wj,注册接口：805322f5f7e5473fba2b10ce0700c930
00:15:23.566 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 物料属性分类 应用Id: 8d81c65b52fc6c70d9665b1ab341d895
00:15:23.566 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料基础信息,注册接口：1cb07fcb254e4450b8c19f821b52e25d
00:15:23.567 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件销毁表单
00:15:23.568 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用QQ,注册模型：子流程
00:15:23.568 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用公共接口工具,注册接口：fd4d4861ae59b58d99607d6edcde55e1
00:15:23.568 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料属性分类,注册视图：物料属性分类PC
00:15:23.568 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用户管理,注册视图：用户管理界面
00:15:23.568 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用报告流程管理,注册触发器：报告流程-提交后修改结果汇总信息
00:15:23.569 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 检测项目管理 应用Id: 4135231aefaef62f9fb530863cdbcff3
00:15:23.569 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用样品检测管理,注册接口：c5a7756490fb8f07927efbb36c5da922
00:15:23.569 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用落尘管理,注册视图：落尘趋势图
00:15:23.569 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型_wj,注册接口：80f70ce1b5f249808e8279c85ac1b4c5
00:15:23.569 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用巡检管理,注册视图：PQC巡检计划
00:15:23.569 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：372044d7354c41b4a119e66f30290a00
00:15:23.571 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用基础数据,注册接口：f21d4561ce31470f9951bc7aadad3be5
00:15:23.571 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料基础信息,注册模型：物料类型
00:15:23.571 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检测项目管理,注册视图：检测项目管理列表
00:15:23.571 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用户管理,注册视图：系统用户
00:15:23.572 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用QQ,注册流程：null
00:15:23.572 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用公共接口工具,注册接口：f68bebaee26cb33c9912494915866e32
00:15:23.572 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用客户投诉管理,注册接口：0754c9a06b784f789fda493bf69c5c9a
00:15:23.573 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用样品检测管理,注册接口：048fcaef3557b2e13099e8a327849395
00:15:23.573 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检测项目管理,注册视图：检测项目管理表单
00:15:23.572 [ForkJoinPool.commonPool-worker-16] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺路线配置,注册接口：f173ea748d7d4050946278f101b249cb
00:15:23.573 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仪表配置,注册视图：仪表区域列表
00:15:23.572 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据管理,注册视图：巡检
00:15:23.572 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用金属地图,注册接口：b4d53c673c4e4deb8090e2a031e8659d
00:15:23.572 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工艺模型_wjtest,注册模型：11
00:15:23.573 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：213bfadb84c24d859f0c1ac7ecf0cf66
00:15:23.574 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备台账,注册视图：二级设备
00:15:23.573 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型_wj,注册接口：44965bfea1584c8390c2d4b637e319e9
00:15:23.575 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料基础信息,注册模型：物料料管理
00:15:23.575 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仪表配置,注册视图：能耗仪表
00:15:23.575 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备基础数据管理,注册视图：测试
00:15:23.577 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备台账,注册视图：设备台账
00:15:23.577 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用样品检测管理,注册接口：47a288ebe72ba748e0c6ab0ffcc04b0d
00:15:23.577 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用公共接口工具,注册接口：0992462203a3a1f4884e1328701ab978
00:15:23.578 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：3e8457ad8d8d4f899045188dd351971a
00:15:23.578 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工艺模型_wjtest,注册模型：22
00:15:23.578 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用QQ,注册触发器：消息触发
00:15:23.578 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用用来测试的,注册接口：5478b2a26725469390dc033d367359df
00:15:23.578 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用QQ,注册触发器：qxw
00:15:23.578 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型_wj,注册接口：a81e2bdcba1146aeaa4bb63c6a63a3fc
00:15:23.579 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 报告权限管理 应用Id: 022609ca997aef5fec87c31d5a2ecf8b
00:15:23.579 [ForkJoinPool.commonPool-worker-16] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺路线配置,注册接口：7b00d32bc9724f05a3cd3e42885b233e
00:15:23.580 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 文件管理 应用Id: d0addb60ef6baa043d74167c4d373939
00:15:23.581 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用样品检测管理,注册接口：7c8e4c38e2cdac3591ab707dd6483179
00:15:23.581 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用公共接口工具,注册接口：c51c65d9d0857400a2c534c41d77e6da
00:15:23.582 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件管理
00:15:23.581 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用报告权限管理,注册视图：报告权限管理列表
00:15:23.582 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备基础数据管理,注册接口：f7cee93755309a5087b15e6e939677fc
00:15:23.582 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用落尘管理,注册接口：598fcdcf63fc4e6583a8cef7c562722f
00:15:23.582 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备基础数据,注册接口：12e1c484c376422a82c4632fa0a13a01
00:15:23.582 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：c995f391f9db4639b80b16b27f3b978c
00:15:23.583 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工艺模型_wj,注册模型：工艺模型审批
00:15:23.583 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工艺模型_wjtest,注册模型：33
00:15:23.583 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用金属地图,注册接口：676719b0d7364343921af6f69b710d61
00:15:23.584 [ForkJoinPool.commonPool-worker-16] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 成品库存 应用Id: 1538078b8960fb3099512a04eae8d471
00:15:23.584 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用客户投诉管理,注册接口：6cc78db4bd7c4e2c95f9725aceb28db1
00:15:23.584 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用报告权限管理,注册视图：报告权限管理表单
00:15:23.585 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用样品检测管理,注册接口：92cc2ab239d8153a72f81def53a8b25a
00:15:23.585 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 对比分析 应用Id: cbb9e6e1c45ed5657fcc257ef24c4f17
00:15:23.585 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备管理,注册视图：设备台账表单
00:15:23.586 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备基础数据,注册接口：48368a43a746b1d01e66ba2eea112eb4
00:15:23.586 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：af8f04dddce44acf8cb90e7e0f19dec9
00:15:23.586 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工艺模型_wjtest,注册模型：44
00:15:23.587 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用工艺模型_wj,注册流程：null
00:15:23.587 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用对比分析,注册视图：单仪表对比分析
00:15:23.586 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用基础数据,注册接口：d7544d3221834c5589d2c5117e5886ee
00:15:23.588 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用样品检测管理,注册接口：6a663a0563f19bb9ce25014560248e53
00:15:23.590 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用用来测试的,注册接口：3b8502d33e9a4adfb4e16bdc4a2077a6
00:15:23.590 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用对比分析,注册视图：多仪表对比分析
00:15:23.590 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用标准物质管理,注册视图：基准溶液(JZ)配置表单
00:15:23.590 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：7709630d349e45bc86842dbe37cebba1
00:15:23.591 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工艺模型_wjtest,注册模型：55
00:15:23.592 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用检测项目管理,注册模型：检测项目管理表单
00:15:23.592 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用对比分析,注册视图：耗电同环比分析
00:15:23.592 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用入场物资,注册视图：入场物资检测成分
00:15:23.592 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备基础数据,注册接口：3c82d79900184e27b71ad238d581c2e3
00:15:23.594 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：e4903aabcb174c51bcd4040e72867499
00:15:23.595 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用金属地图,注册接口：14937e64b91a4077a2404d62d7a140cb
00:15:23.595 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用用来测试的,注册接口：ff2b1b5c1a5f4808b0cf28caba53bfc6
00:15:23.595 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：5a5bbec7d2399dc58c3517efc3c89182
00:15:23.594 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工艺模型_wjtest,注册模型：66
00:15:23.594 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用样品检测管理,注册接口：dc53afffd979c881fc27289dc0b6ada9
00:15:23.594 [ForkJoinPool.commonPool-worker-53] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用对比分析,注册接口：d894f8897de44fe40dfbb8461dda4b54
00:15:23.596 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检测流程,注册视图：检测流程表单
00:15:23.597 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用客户投诉管理,注册接口：9d9813cd60db49d5800d6563fdd7277b
00:15:23.597 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备基础数据,注册接口：ba63e0563d50f388442006704e796ee9
00:15:23.597 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 危化品仓管理 应用Id: 6bc8f242ee53159bb051a10fdc246f55
00:15:23.597 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备检定,注册接口：ef097d0c83c75b3ec7dc91104d6f495a
00:15:23.598 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用用来测试的,注册接口：82960643a77f4b67870d73b9707afd00
00:15:23.598 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用样品检测管理,注册模型：任务配置&超限值(L)设置
00:15:23.599 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：c1a0964852a14a8ca5589753f8f7dfbc
00:15:23.598 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品信息列表
00:15:23.599 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工艺模型_wjtest,注册模型：88
00:15:23.600 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：725108ea25a4fbf40a82109dfa8cb004
00:15:23.600 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用基础数据,注册接口：12cd74a646f04ab08fbccf1594f3c0db
00:15:23.601 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备基础数据,注册接口：b33511006551736e8c8868364f379afc
00:15:23.601 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用用户管理,注册模型：系统用户
00:15:23.601 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报告权限管理,注册接口：3aab135cd3e76fe885f9dc618181b147
00:15:23.601 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：a02e317ea4e94b958b0cb9d2be01b16b
00:15:23.601 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品实时库存列表
00:15:23.601 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用入场物资,注册视图：入场物资记录数据管理视图
00:15:23.601 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备检定,注册接口：f5abbcd1421e5e4a96801cf42ba310dd
00:15:23.602 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工艺模型_wjtest,注册模型：100
00:15:23.602 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 工艺模型设计 应用Id: 1cca616abd27da9a60ec69413232ab22
00:15:23.603 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用基础数据,注册接口：2a323a4489b24c8e863f19e8a392da53
00:15:23.603 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件作废表单
00:15:23.603 [ForkJoinPool.commonPool-worker-20] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用样品检测管理,注册模型：样品检测管理表单
00:15:23.604 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用入场物资,注册接口：1b3e95363e6b4be0a44fa21c5935852f
00:15:23.604 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品库存日志列表
00:15:23.604 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：65c0d1f495050c276d02c1e66560ed81
00:15:23.604 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型设计,注册视图：工艺模型流程审批
00:15:23.604 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用金属地图,注册接口：a10c5c7d0bca45fb800354f05beb1124
00:15:23.605 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型-lyh,注册接口：a7db8aac61ed4f16b276a1ea257ff45c
00:15:23.605 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备基础数据,注册接口：52e485c6b6781e54d0f7cbd44e22ff5b
00:15:23.607 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用用户管理,注册模型：系统用户
00:15:23.607 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用人员管理,注册视图：人员岗位培训表单
00:15:23.607 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报告权限管理,注册接口：ad53f3ab011d079595c86695b2213882
00:15:23.607 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备检定,注册接口：ee206a393132ea7381831c11a9eec7d5
00:15:23.607 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品入库登记列表
00:15:23.608 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：6079a92d22afb5cc404829761ff72b70
00:15:23.608 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工艺模型_wjtest,注册模型：data-table
00:15:23.609 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 工艺路线 应用Id: b9fca387292a4121af6cfbc468841d22
00:15:23.609 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品盘点登记列表
00:15:23.609 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工艺模型-lyh,注册模型：data-table
00:15:23.610 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备基础数据,注册接口：809a2dc2777769568c537fe125d479be
00:15:23.610 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用客户投诉管理,注册接口：771ed12f73d84722a4b685633661f39d
00:15:23.610 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报告权限管理,注册接口：bb49978f6b76dc884b9c3fe1ef51ac50
00:15:23.611 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料类型表单
00:15:23.611 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备检定,注册接口：fcb9aba77578598e578c7cab4d910823
00:15:23.611 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺路线,注册视图：工艺路线
00:15:23.611 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品领用登记列表
00:15:23.611 [ForkJoinPool.commonPool-worker-31] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用用户管理,注册模型：系统用户
00:15:23.612 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：78e34067fe14e479e8b61c353b6e3913
00:15:23.613 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：班组当班库存列表
00:15:23.613 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用报告权限管理,注册模型：报告权限管理表单
00:15:23.613 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工艺模型_wjtest,注册模型：101
00:15:23.614 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备基础数据,注册接口：f38ab277653d4cbea553c1eaed863dc6
00:15:23.614 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用入场物资,注册接口：be4ba0cae4994784b925048ba377db39
00:15:23.614 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备检定,注册接口：cfb6fe2fdb2080f32cdcf7f2a7990400
00:15:23.614 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工艺模型-lyh,注册模型：data-table
00:15:23.614 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：班组库存日志列表
00:15:23.615 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：b4b77d47b190989071d83dab79c92446
00:15:23.615 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用金属地图,注册接口：783deca0423a4ab1a21f9a59a06873db
00:15:23.615 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用工艺模型_wjtest,注册流程：null
00:15:23.616 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品退库登记列表
00:15:23.617 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备基础数据,注册接口：e2b9781f54ba435b9400812d752086d1
00:15:23.617 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：14e7479194b4b10a7ee84f4550a19245
00:15:23.617 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备检定,注册模型：检定计划表单
00:15:23.618 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：交接班管理列表
00:15:23.618 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工艺模型-lyh,注册模型：工序名称
00:15:23.618 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用客户投诉管理,注册接口：a8e2103b3e8d4e11b0654d740f40c293
00:15:23.618 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仪表配置,注册视图：能耗仪表列表
00:15:23.620 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用巡检管理,注册视图：PQC巡检项目
00:15:23.620 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：18230fa65bddc344d43a64d6dd43bade
00:15:23.620 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：库存预警
00:15:23.620 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品实时库存表单
00:15:23.620 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备基础数据,注册接口：e658c37f39064b959a29fedd0e5aafcd
00:15:23.621 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仪表配置,注册视图：区域结构配置
00:15:23.621 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备检定,注册模型：设备检定表单
00:15:23.623 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用入场物资,注册接口：dfd49017aa3546d29d336f02b58d190b
00:15:23.623 [ForkJoinPool.commonPool-worker-24] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 测试 应用Id: 37eb316275bc5705e6fb0a58315001b0
00:15:23.623 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型设计,注册视图：工艺模型模板
00:15:23.623 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：e4bd753e88a751ff45cb83b7304f82f0
00:15:23.623 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备基础数据,注册接口：19da1aedff3341d68171ef1a27b754c5
00:15:23.624 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工艺模型-lyh,注册模型：物料bom
00:15:23.627 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用金属地图,注册接口：bfd9ec15e3134c20aef03b00be66d14f
00:15:23.628 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：6482bde39fefcd6023afaaf49af58dab
00:15:23.628 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：出入库统计查询
00:15:23.629 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用客户投诉管理,注册接口：ba19f2cd0a854a56b8fa54b6c04a5b7d
00:15:23.630 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 外部字典数据应用 应用Id: 394e402ca5254786998a176ddc27ff5f
00:15:23.630 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工艺模型-lyh,注册模型：工艺模型流程视图
00:15:23.630 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺路线,注册接口：671776df85e54491b95bbcc2419c5d91
00:15:23.631 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：d81e0657bc05f409ef1ca6d0f707b767
00:15:23.631 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仪表配置,注册视图：区域结构配置树列表
00:15:23.631 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用外部字典数据应用,注册视图：主表
00:15:23.632 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 0507-2 应用Id: 15e18bb6f07e87dfe6e3e32381c6471c
00:15:23.633 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仪表配置,注册视图：计算规则
00:15:23.633 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件类型
00:15:23.633 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用工艺模型-lyh,注册流程：null
00:15:23.633 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用0507-2,注册视图：列表
00:15:23.633 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备基础数据,注册接口：034fe0682ea141dc94fdbf871ea6d9af
00:15:23.634 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用外部字典数据应用,注册视图：子表
00:15:23.634 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：班组当班库存表单
00:15:23.634 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 物料属性分类 应用Id: 3fa1d145339bc4b8f809ea805731f546
00:15:23.633 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用入场物资,注册接口：81b3d73782154773abc622a1762aaaad
00:15:23.634 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件发放表单
00:15:23.635 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用0507-2,注册视图：测试
00:15:23.636 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料盘点表单
00:15:23.637 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料属性分类,注册视图：物料属性分类
00:15:23.638 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用金属地图,注册接口：c4a8e7289e094e3bba946b8694a09fd4
00:15:23.641 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用外部字典数据应用,注册视图：demo221133
00:15:23.641 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用客户投诉管理,注册模型：客户投诉管理
00:15:23.644 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺路线,注册接口：07b01a4f850a47f8ab069b14faf1eb8a
00:15:23.644 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用0507-2,注册接口：6ddc1a68353c4c4ea8987a54ac3d271a
00:15:23.644 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用金属地图,注册接口：12e3b13da00a4ea5bc093123b66794e8
00:15:23.645 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用外部字典数据应用,注册视图：主表
00:15:23.646 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备维修,注册视图：委外管理
00:15:23.647 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用客户投诉管理,注册模型：原因分析模型
00:15:23.647 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备基础数据,注册接口：e22bc7fa5cd448adb48fb8eb1e7c5ae9
00:15:23.647 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件类型数据视图
00:15:23.648 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用入场物资,注册接口：9b9901d54a814f60b08921c7380c7e8b
00:15:23.648 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用金属地图,注册模型：质量管理-金属地图-金属地图
00:15:23.648 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用用来测试的,注册接口：2a0259c9dfc14db0bae6bb23b9bd2808
00:15:23.648 [ForkJoinPool.commonPool-worker-26] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用0507-2,注册模型：测试
00:15:23.649 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原合同清单,注册视图：测试流程
00:15:23.649 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：修改文件权限
00:15:23.649 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仪表配置,注册视图：计算规则列表
00:15:23.650 [ForkJoinPool.commonPool-worker-49] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 工艺模型_wj 应用Id: 49ea6e978131d13478150d3940c15cc9
00:15:23.651 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原合同清单,注册视图：简单流程
00:15:23.651 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仪表配置,注册视图：尖峰平谷配置
00:15:23.652 [ForkJoinPool.commonPool-worker-61] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用客户投诉管理,注册流程：null
00:15:23.653 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用金属地图,注册模型：设备金属含量
00:15:23.653 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用用来测试的,注册模型：测试模型
00:15:23.653 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品库存日志表单
00:15:23.656 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺路线,注册接口：29e68f85cebd47328053e3c09ffed4ea
00:15:23.657 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用用来测试的,注册模型：子表
00:15:23.660 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用金属地图,注册模型：质量管理-金属地图-金属地图设备
00:15:23.660 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备基础数据,注册模型：设备列表
00:15:23.662 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用入场物资,注册模型：入场物资记录详情表
00:15:23.663 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用用来测试的,注册模型：简单的流程
00:15:23.663 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 名称 应用Id: 12c34562c9c66cc1f617c2bc49e25f68
00:15:23.664 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：36f4292693867cf98110936c784aadb6
00:15:23.664 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用外部字典数据应用,注册接口：bbff5394122409728798e8f7a363ce8a
00:15:23.664 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料出库表单
00:15:23.665 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仪表配置,注册视图：尖峰平谷配置列表
00:15:23.665 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备基础数据,注册模型：检定记录模板管理表单
00:15:23.665 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用名称,注册视图：测试
00:15:23.666 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用人员管理,注册接口：5de31b16314c210edb053ae1d840222a
00:15:23.667 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用仪表配置,注册接口：0054cd7bb31e9dfaec4e9cdad9efab74
00:15:23.667 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用入场物资,注册模型：入场物资记录表
00:15:23.667 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用名称,注册视图：视图测试
00:15:23.669 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备基础数据,注册模型：作业流程管理表单
00:15:23.669 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用委托流程,注册视图：样品委托流程列表
00:15:23.669 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用外部字典数据应用,注册接口：ba49c4dda1cf7bcf3d0c4e2915d39566
00:15:23.670 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用名称,注册接口：b58f888709214a1b81fee54896e19cd5
00:15:23.670 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺路线,注册接口：435f2215ee4e407f81514c3317707716
00:15:23.672 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：4098196f38a9d1bef4a83dbc4cbc8fe4
00:15:23.673 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用标准物质管理,注册视图：标准物质基础配置表单
00:15:23.673 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备基础数据,注册模型：计量设备管理表单
00:15:23.673 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用外部字典数据应用,注册模型：子表
00:15:23.673 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用仪表配置,注册接口：014da3b5487e4aef73e20f531e5155ba
00:15:23.675 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用用来测试的,注册流程：null
00:15:23.676 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用巡检管理,注册视图：PQC巡检执行
00:15:23.677 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用外部字典数据应用,注册模型：demo221133
00:15:23.677 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检测流程,注册视图：检测流程列表
00:15:23.677 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备基础数据,注册模型：自检计划表单
00:15:23.678 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件变更视图
00:15:23.679 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用仪表配置,注册接口：5e106e7c0c13182a559250ab52106a58
00:15:23.679 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件借出表单
00:15:23.679 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用入场物资,注册模型：入场物资检测成分
00:15:23.679 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检测流程,注册视图：结果汇总表单
00:15:23.679 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：2bae6e18a3744e6c1b58be4c0216970e
00:15:23.680 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备台账,注册接口：9caf16eb28486d23dc1869a0977c2fab
00:15:23.681 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备维修,注册接口：7fa29afd52524b68ba5f821a15ca443e
00:15:23.681 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用外部字典数据应用,注册模型：主表
00:15:23.681 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用原合同清单,注册接口：fd74760c62b5422bb8fc14ed8b2a7d97
00:15:23.682 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：3e12ab2e159ab5714c841ed63661467a
00:15:23.681 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用名称,注册模型：测试
00:15:23.682 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用仪表配置,注册接口：e7eca095bf5093464d855d33facdeb0f
00:15:23.682 [ForkJoinPool.commonPool-worker-34] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用入场物资,注册流程：null
00:15:23.682 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备台账,注册接口：21ced69906f1d48b6240596f2e7951a1
00:15:23.683 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型设计,注册视图：审批流程
00:15:23.684 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 线边库管理 应用Id: f2a75135b8ad73cc8a0d16fe11578bdb
00:15:23.684 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 物料BOM 应用Id: b8e76ecffd6eb49b704cff3a23ec1e55
00:15:23.685 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：8e4dfc26298d798ea43c30e5e72d0e02
00:15:23.687 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料BOM,注册视图：物料BOM
00:15:23.687 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用线边库管理,注册视图：线边库类型
00:15:23.687 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用巡检管理,注册视图：PQC巡检计划记录
00:15:23.688 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：5d786ce690a79f34be24da55d6f0030d
00:15:23.689 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备台账,注册接口：c7a7d384100a5558b18ee6e99d940aa4
00:15:23.689 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用用来测试的,注册触发器：测试流程的提交、结束
00:15:23.690 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用设备基础数据,注册触发器：自检计划-提交后修改计量设备状态
00:15:23.690 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品信息表单
00:15:23.690 [ForkJoinPool.commonPool-worker-47] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用用来测试的,注册触发器：数据模型
00:15:23.690 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用设备基础数据,注册触发器：外检计划-提交后修改计量设备状态
00:15:23.690 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用标准物质管理,注册视图：领取任务
00:15:23.691 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 检定流程 应用Id: 2ed126c6298dfc232175b69b2ea6a567
00:15:23.691 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用线边库管理,注册视图：线边库管理
00:15:23.691 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料入库表单
00:15:23.691 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备台账,注册接口：9c1b239d8983ab8ffcbfd655332950dc
00:15:23.692 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：f4e1b9340d1ea146d1a629acc572eeda
00:15:23.695 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检定流程,注册视图：送检登记列表
00:15:23.693 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备维修,注册接口：0a50757ccd5f41f691300b950d231f7d
00:15:23.695 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备台账,注册接口：fbc5d1c7df43e62246edabc8ebf8afbe
00:15:23.696 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检定流程,注册视图：我的任务
00:15:23.696 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：e693ffa67012a92526381b70ddb1007d
00:15:23.696 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: hhh 应用Id: c546fb9f0615130d75189ab560c4216f
00:15:23.697 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检定流程,注册视图：设备结果审核表单
00:15:23.697 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用hhh,注册视图：dict2
00:15:23.697 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备台账,注册接口：d2a664c2eb830f95045737f384d74261
00:15:23.698 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用巡检管理,注册视图：PQC巡检计划数据视图
00:15:23.698 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：2a7d831733f7fefec84f889708f3f2f9
00:15:23.698 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检定流程,注册视图：送检登记表单
00:15:23.699 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用标准物质管理,注册视图：标准滴定溶液(BY)配置表单(主任务发布)
00:15:23.699 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用巡检管理,注册接口：4667c6704aec41c88090b60c66201a89
00:15:23.699 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备台账,注册接口：32ddcfa52ff9ff9d9c197bd549af739d
00:15:23.700 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用原合同清单,注册接口：92f0e5c7611d87d6b4716cc9030fb600
00:15:23.700 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：ef1d78bbf37b9ae40a679ddd0f984eae
00:15:23.700 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：班组库存日志表单
00:15:23.701 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用线边库管理,注册视图：测试视图
00:15:23.702 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用原合同清单,注册接口：8bb04886c36afc3cbaefbc9e3a498c99
00:15:23.702 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备维修,注册接口：8326b3db3a714b7d95f9c33b600579dd
00:15:23.703 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用线边库管理,注册视图：线边库管理
00:15:23.703 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：d22181278716b81f14ac29dace84c3e3
00:15:23.703 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件树图
00:15:23.703 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用hhh,注册视图：dict1
00:15:23.703 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备台账,注册接口：1570abad49da97eb915f8893c6d76245
00:15:23.704 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用线边库管理,注册视图：测试
00:15:23.705 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用原合同清单,注册接口：54bf871d11cf9ccf66cc8c0a5e38b08e
00:15:23.705 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：ffd74bb2a1be3eb6384af06abcd8515e
00:15:23.706 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用巡检管理,注册接口：db31298c0d7446b7b6de9b3706832185
00:15:23.706 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备台账,注册接口：12421f8b814f893242d2a74450edf15b
00:15:23.706 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用原合同清单,注册接口：bee4464c10d59a63f8e10caed219de40
00:15:23.707 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用线边库管理,注册接口：80e3297ee1a14ab5a10d71c5400f3c03
00:15:23.707 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用工艺模型设计,注册视图：审批流程视图
00:15:23.708 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：6622e22037e8a95a8552bb785f84e961
00:15:23.708 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备台账,注册接口：a8bd87266c67e73c588103836702b437
00:15:23.708 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用hhh,注册视图：dict1manger
00:15:23.708 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：001c52c62959d0a31a5e8a9165bdb7b6
00:15:23.708 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用原合同清单,注册接口：fdb16b38bdcc841f0c84a6263dff08f1
00:15:23.708 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用线边库管理,注册模型：线边库类型
00:15:23.709 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：b602f4741ceb47c69ed2db527b4afd3f
00:15:23.709 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用hhh,注册视图：dic2manger
00:15:23.709 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备维修,注册接口：71758676aa7b4e419add87dc641e03e6
00:15:23.710 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用hhh,注册接口：62bfc4a30b99c08e0b79dde8634b4410
00:15:23.710 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备管理,注册视图：设备检定表单
00:15:23.710 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备台账,注册模型：设备台账表单
00:15:23.710 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：d25b0ac9e5f43359e77e08165343a791
00:15:23.710 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：ea8bbd4dab296987971f5a1afb64a08f
00:15:23.711 [ForkJoinPool.commonPool-worker-56] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用线边库管理,注册模型：线边库管理
00:15:23.712 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检定流程,注册视图：检定任务认领
00:15:23.713 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料BOM,注册视图：物料BOM列表
00:15:23.713 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用hhh,注册模型：data-table
00:15:23.713 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备台账,注册模型：二级设备表单
00:15:23.714 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用巡检管理,注册接口：1649983030bc41fa808035c3e6ccdcfa
00:15:23.714 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：3fccb7b843474c2c3114c032d7d96ccf
00:15:23.714 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：c90bba4c253bdc14398d35d2be5fc1aa
00:15:23.715 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料BOM,注册视图：物料BOM管理
00:15:23.716 [ForkJoinPool.commonPool-worker-3] INFO  c.l.i.LdfBusinessConfig - [lambda$register$9,223] - 注册应用设备台账,注册条码：设备铭牌
00:15:23.716 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料BOM,注册模型：物料BOM明细
00:15:23.716 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：3411b92bd1fb0693a370583dd5f4d327
00:15:23.716 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用原合同清单,注册模型：子表
00:15:23.717 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：10614704528270cdf079f6db631bdff3
00:15:23.717 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备维修,注册接口：cfcdf51aeaab49d283649e969513b4e8
00:15:23.717 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用hhh,注册模型：dict2
00:15:23.717 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：92f3642b807a425883c13b2a619612ed
00:15:23.718 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：7b4e632e921814834fcb1a7dce837e46
00:15:23.718 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用hhh,注册模型：dict1
00:15:23.719 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检定流程,注册视图：设备检定表单
00:15:23.719 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料BOM,注册模型：物料BOM
00:15:23.719 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用原合同清单,注册模型：原合同清单
00:15:23.720 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料仓管理,注册接口：21dc852508ddaeed45b8a084d81f32a3
00:15:23.720 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：7dbc81f6739fdb47d21fc041aa1eb095
00:15:23.720 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品预警查询PC
00:15:23.720 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用巡检管理,注册接口：56a0c56b58a64e1ab1af58d01218f0bb
00:15:23.721 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：d33689109538dba8af0ac75da07f1ca6
00:15:23.721 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用hhh,注册流程：null
00:15:23.722 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用原合同清单,注册模型：一个新的数据模型
00:15:23.722 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：5f51cd334ee82848aac752f7087015a5
00:15:23.722 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备维修,注册接口：f58715eca6604fe6892e418abbd3f28b
00:15:23.723 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料仓管理,注册接口：b2c5c8886493f4a8c5d913a5f84e4326
00:15:23.723 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：7f4346082dc137e0eb780c2f78ca3810
00:15:23.723 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用hhh,注册流程：null
00:15:23.723 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用原合同清单,注册模型：测试流程
00:15:23.724 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品入库登记表单
00:15:23.725 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料仓管理,注册接口：b57d3bb13cd3ddf510803a4a10277604
00:15:23.725 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：3dbc1192c80a4fc19c3e3b03dfb1518c
00:15:23.725 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用原合同清单,注册模型：简单流程
00:15:23.725 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用巡检管理,注册接口：0194e5627344437bb569ab2008ad6e5a
00:15:23.725 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件管理操作视图
00:15:23.726 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：9a649ad18666fb1764443bbbee13ffda
00:15:23.741 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：76023ab52cc87de2815180f5909b17a4
00:15:23.742 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料仓管理,注册接口：2c364ef92b5c7481784b12f1d9059816
00:15:23.742 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用原合同清单,注册流程：null
00:15:23.743 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：b03781d02b5d1feb504e8135efb1a995
00:15:23.743 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备维修,注册接口：7c5b94997f924979906e200a3702fac7
00:15:23.743 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：c31622222aad08dbc0b17ec4cfc83fa5
00:15:23.744 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料仓管理,注册接口：bf5897cd0a7d3df0023d62ee086f9985
00:15:23.744 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：fe191216fa2f4f4eb423152a802bc2d1
00:15:23.745 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：850e3f9664915477a442abf094544118
00:15:23.745 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用巡检管理,注册接口：575b58c61dec4355b8f668f8ba4f02ca
00:15:23.746 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料仓管理,注册接口：24c8ed05a4d654873e75c704d75a59b1
00:15:23.747 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备管理,注册视图：期间核查表单
00:15:23.748 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：731719cb4cac21d2be6e9bc4f947d63b
00:15:23.748 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料仓管理,注册接口：d980fbeca435afc62fd6fbba55d96399
00:15:23.748 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备维修,注册接口：b26d5f78c5a547f890ecd255a8ef6d8b
00:15:23.749 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：00369e0cc0933f90784d5d92706c69b0
00:15:23.750 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：4ec5339f5f534a65986fe1beb5488687
00:15:23.750 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：1ec6d3b5c51ec4ed8a100dd752e0c63c
00:15:23.751 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用巡检管理,注册接口：7bbc70f1699b4f65aa878eecd0fef82f
00:15:23.751 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料仓管理,注册接口：ee8996ec871731755a4d51f68043dc8a
00:15:23.752 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：6f48ab142000f287fb75d21d56a23215
00:15:23.753 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料仓管理,注册接口：2687dcce45c8f5a6420fd92df1bb6b72
00:15:23.753 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备维修,注册接口：da76c2dffbc54d229f005f70a3e34f20
00:15:23.753 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品领用登记表单
00:15:23.754 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用标准物质管理,注册视图：标准滴定溶液(BY)标定（子任务领取）
00:15:23.755 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：e3283c60da7ff7bf8c10602548455592
00:15:23.755 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托流程,注册接口：d9d7aeee2b34bd20221abb9f9a35d6f1
00:15:23.755 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：5b08b01f6e5447b993794f2f8d78b119
00:15:23.755 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料仓管理,注册接口：3d1360e69ea7a8cc05bb42c103f74cbb
00:15:23.756 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用巡检管理,注册接口：69ea8821fdbc465f9a2f10fa83eaf220
00:15:23.756 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用原合同清单,注册流程：null
00:15:23.757 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：4a5d6615d275b7c34b4090ba0b0b5a81
00:15:23.758 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：88ff160c5ad240d7bd5cc06268ee9c0c
00:15:23.758 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检定流程,注册视图：设备检定审核表单
00:15:23.758 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料仓管理,注册接口：31d1c4df5c006d5a83cf9fb0fea5c3ec
00:15:23.759 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：db40eca131a0d0894cf6292518869559
00:15:23.759 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备维修,注册模型：设备维修
00:15:23.760 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料仓管理,注册接口：498c0ba468149baea26a005f840c9eb9
00:15:23.760 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：2c36066a90314a57605650c7003e4a4b
00:15:23.761 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：6ee0acbfbd8b4f02b62257993cf32c5c
00:15:23.762 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用巡检管理,注册接口：37ff3ddc92444d56b2ca994c2c302dbd
00:15:23.762 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用委托流程,注册模型：样品列表
00:15:23.763 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：7506ad2734884a9480e4d2329568d739
00:15:23.763 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用文件管理,注册模型：文件台账表单
00:15:23.763 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料仓管理,注册接口：95ad69287788db948adff3a43d6cdca0
00:15:23.765 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备维修,注册模型：委外管理
00:15:23.765 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料仓管理,注册接口：3fd367811ae9031839f5ae764aa81ec6
00:15:23.765 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用文件管理,注册模型：文件柜号表单
00:15:23.766 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：a5060066c78d4ada9d966db9d1c893ef
00:15:23.766 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用委托流程,注册模型：方法依据列表
00:15:23.767 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料仓管理,注册接口：0d4435463039ef76f020b654411574d9
00:15:23.767 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用巡检管理,注册接口：375eb80f2b174749a6e3dc5d1fe82dff
00:15:23.767 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用文件管理,注册模型：文件柜位表单
00:15:23.768 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品退库登记表单
00:15:23.769 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用委托流程,注册模型：加密换袋列表
00:15:23.769 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用文件管理,注册模型：文件入库表单
00:15:23.769 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：f92d3616c12f46599a349ecc89e99f26
00:15:23.770 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料仓管理,注册接口：23b6c0a13c63c7414bc0983c3a67c85f
00:15:23.770 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用设备维修,注册流程：null
00:15:23.771 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：641fffbf5a91423499401622c57082ee
00:15:23.770 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用文件管理,注册模型：文件销毁表单
00:15:23.771 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用委托流程,注册模型：收样列表
00:15:23.772 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料仓管理,注册接口：39fbab7da4699803efd0c71f095c734c
00:15:23.772 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测流程,注册接口：e74dd7371d74633ff72eff87df05f8d2
00:15:23.772 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：72a1bc2f575f4445bf755b8e82dec591
00:15:23.773 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用巡检管理,注册接口：bc715a95399449a69a3dc04431fec76e
00:15:23.773 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用文件管理,注册模型：文件作废表单
00:15:23.773 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料仓管理,注册接口：fe49c3ed2a6f5b2cd9521fbceae39274
00:15:23.774 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用委托流程,注册模型：样品委托流程表单
00:15:23.775 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料仓管理,注册接口：b491748517822f97d959ed3bf48151c9
00:15:23.775 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测流程,注册接口：ebf8bbffcb252223cfe10305b7f7d6b8
00:15:23.775 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：1817d00134f94ed0a5aeba4df012723b
00:15:23.775 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用文件管理,注册模型：文件发放表单
00:15:23.776 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料仓管理,注册接口：41a7dc3c494191f157f51b07c84c5735
00:15:23.777 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测流程,注册接口：971b159101665115ddd602bdf5d8bbdf
00:15:23.777 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用文件管理,注册视图：文件夹管理
00:15:23.777 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用巡检管理,注册接口：347e0bb8874948c795a41340d5af844a
00:15:23.778 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用文件管理,注册模型：文件借出表单
00:15:23.778 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：3febe605ed5542edb00c86e52a76add4
00:15:23.779 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检定流程,注册接口：9ef0fca4af174129932b4ba15d7b6af0
00:15:23.779 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料仓管理,注册模型：物料信息表单
00:15:23.780 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测流程,注册接口：06be3f3a54938812cf9eabfd4e9ad6b3
00:15:23.780 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用文件管理,注册触发器：文件入库后修改台账信息
00:15:23.781 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: test设备 应用Id: 0b9b320acf7d1d49eaebb23cf921887c
00:15:23.781 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检定流程,注册接口：6f9f0fa74a894159bec335d8d46bf3e8
00:15:23.781 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：6c45e6c309ceae226024f03034238066
00:15:23.781 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用test设备,注册视图：学生表
00:15:23.781 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测流程,注册接口：b980d49466fca76d84175fc6ffb8e3a8
00:15:23.781 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料仓管理,注册模型：物料库存表单
00:15:23.782 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：d1b09052b8b24f67b78987b67e3b1963
00:15:23.784 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用巡检管理,注册接口：6ce65c8539514813b6745391a3c56358
00:15:23.784 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检定流程,注册接口：d0b0d0dac25d4104a7e2f5b0598d882e
00:15:23.783 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用原合同清单,注册触发器：原合同清单触发器新增
00:15:23.784 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：cd7ab9c7bdd33a20398191380e3e4da0
00:15:23.784 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料仓管理,注册模型：物料预警表单
00:15:23.784 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品盘点登记表单
00:15:23.785 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测流程,注册接口：d4268087e0806a5feaaefb0bb0068f65
00:15:23.786 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：bdf81345777e467f954e173b3fbad451
00:15:23.787 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：a33e5bedfb78e559c1067a67a5874bc9
00:15:23.787 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料仓管理,注册模型：库存日志表单
00:15:23.787 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测流程,注册接口：231704637610419988d0595236c593f4
00:15:23.786 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检定流程,注册接口：3f08b0ce84d20175f10b177a8528ab64
00:15:23.788 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：58008945e365430c9379b40431b4c587
00:15:23.788 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：5c41113c6562401c9493331aac2dc9be
00:15:23.788 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：d8a36de7243d45e3b0ca7c5f05bf2bce
00:15:23.788 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测流程,注册接口：b847c666da6b38a5e2ac9cef9ee1fb00
00:15:23.788 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料仓管理,注册模型：物料类型表单
00:15:23.789 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检定流程,注册接口：4a3010b31116ce6784b4bf27e4182c03
00:15:23.789 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用test设备,注册模型：学生表
00:15:23.789 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用巡检管理,注册接口：0dd8f54f4942491e90e988dd744dd7c2
00:15:23.790 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料仓管理,注册模型：盘点物料
00:15:23.790 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测流程,注册接口：fa9cdd38ac90282a03622f7bec9f39e0
00:15:23.790 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：a3f335db3aa6ae984afc290817bfe47b
00:15:23.790 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 计量设备总台账 应用Id: e1c6ba8c5fcd52893948d58a5d9f6ee2
00:15:23.791 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：484992ba50684b3ab5a1ea40de392f0e
00:15:23.791 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用计量设备总台账,注册视图：设备类型列表
00:15:23.791 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检定流程,注册接口：ac2500028aa9483db6e2de0f88963fd3
00:15:23.791 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测流程,注册接口：990dd1507802455cc5456c798931e1e7
00:15:23.791 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：e48a2712a63abf02b020a03785f4b30a
00:15:23.792 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用计量设备总台账,注册视图：台账列表
00:15:23.792 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料仓管理,注册模型：物料盘点表单
00:15:23.793 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：3e17284c0d264316a4f72c0d88502053
00:15:23.793 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：c597f2db6aca4839b77f50c315079045
00:15:23.793 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用计量设备总台账,注册视图：台账管理表单
00:15:23.794 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检定流程,注册接口：dbf5fc91f1cdb34422bc6e04cc51d078
00:15:23.794 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测流程,注册接口：555f5f691a705eec0191d0f8218e4c47
00:15:23.794 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：339da88db8852a4873ac9bd1cd18694c
00:15:23.796 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用巡检管理,注册接口：19ca723da67f4cfab9cf1d353570fcd7
00:15:23.796 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：ec7b04e8a913e38b550ac0e04c6ff80e
00:15:23.798 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：79d8b33d28374bb5988f45e10d3fc65d
00:15:23.798 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测流程,注册接口：67d650f5359b8612755528c6544c09fe
00:15:23.799 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检定流程,注册接口：dfebe6fe2302f915e497a087ec3572ff
00:15:23.799 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：75317ffd9c45c14e6a1447a03a3c8220
00:15:23.799 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料仓管理,注册模型：出库物料
00:15:23.800 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用工艺模型设计,注册接口：38cca29292e44bb2ade16cffe32a385d
00:15:23.801 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测流程,注册接口：bf8c3c11db895c34d5343715c11d8620
00:15:23.800 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：91aa0db7192f4241a1466fadc3c05d50
00:15:23.801 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检定流程,注册接口：c2fced667cf2f62a4f8c722e23ef9cb9
00:15:23.802 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：交接班管理表单
00:15:23.802 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：ddf9e095690ad0f80fecddc3c34b1dcd
00:15:23.803 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用巡检管理,注册模型：data-table
00:15:23.803 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工艺模型设计,注册模型：工艺模型流程审批
00:15:23.804 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料仓管理,注册模型：物料出库表单
00:15:23.804 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测流程,注册接口：b71fce122e8539c565bf1c05dd6dd3e1
00:15:23.804 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检定流程,注册接口：578fdcaa76dca4d7560fcda61c45cade
00:15:23.805 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工艺模型设计,注册模型：工艺模型模板
00:15:23.806 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：4d2c8fa53bb63f2950a6d2ff71a6705b
00:15:23.806 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用检定流程,注册模型：设备列表
00:15:23.807 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用巡检管理,注册模型：巡检项目表
00:15:23.807 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测流程,注册接口：48a27b61ae7179b503188d89206b0668
00:15:23.807 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：dbbe661884d44ff6aff9c95ec3101097
00:15:23.807 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用工艺模型设计,注册模型：审批流程
00:15:23.808 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用巡检管理,注册模型：详情
00:15:23.809 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：fc351b1a56acf1a5444965fda0a30444
00:15:23.810 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用检定流程,注册模型：送检登记表单
00:15:23.810 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料仓管理,注册模型：物料入库表单
00:15:23.810 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用工艺模型设计,注册流程：null
00:15:23.811 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：ed1e98cc42cd355ff0b2c3ec22f6df65
00:15:23.812 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用巡检管理,注册模型：巡检记录
00:15:23.812 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测流程,注册接口：ad3b4ee823da52b15ebe2fde0fcbc01e
00:15:23.813 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：e9be2ce1c7804396ab1491e376ba12fe
00:15:23.814 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：a9d2aa6ebe93670cedb88f7eefabd048
00:15:23.815 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用检测流程,注册模型：待任务认领表单
00:15:23.815 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用巡检管理,注册模型：主表格
00:15:23.817 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用计量设备总台账,注册视图：设备类型表单
00:15:23.818 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用设备维修,注册流程：null
00:15:23.818 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用检定流程,注册模型：设备检定表单
00:15:23.819 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：fcfedf013651b6e86fcdd539b8476769
00:15:23.820 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用检测流程,注册模型：检测流程详情
00:15:23.821 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用巡检管理,注册模型：PQC巡检计划
00:15:23.821 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用工艺模型设计,注册流程：null
00:15:23.821 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$9,223] - 注册应用物料仓管理,注册条码：物料信息
00:15:23.822 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：0a49ae78a5025533f34d7e39b4b93f31
00:15:23.822 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用检定流程,注册模型：设备检定审核表单
00:15:23.822 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：660899ada750436ca727d1064c4c8448
00:15:23.823 [ForkJoinPool.commonPool-worker-63] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用巡检管理,注册流程：null
00:15:23.823 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 原辅材来料检验 应用Id: 17304f8867a5765c9357cac060c4ee92
00:15:23.826 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用检定流程,注册触发器：设备结果审核-审核完成后置回调
00:15:23.826 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原辅材来料检验,注册视图：原辅材来料检验
00:15:23.826 [ForkJoinPool.commonPool-worker-4] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用检定流程,注册触发器：送检登记-结束后修改状态
00:15:23.826 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用检测流程,注册模型：称样列表
00:15:23.826 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用设备维修,注册触发器：委外提交
00:15:23.827 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 出货检验 应用Id: 2f0d9d2b6131a5005fa9bd59aedee396
00:15:23.827 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品作废表单
00:15:23.827 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：2021756bded5fc1f3713fe14dbfbdb4e
00:15:23.828 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用出货检验,注册视图：出货检验项目
00:15:23.829 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用计量设备总台账,注册接口：185298667d572ef78f56c12876475f4b
00:15:23.830 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：e1644b9d70438dda471730b0bcb6666d
00:15:23.831 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用检测流程,注册模型：检测流程表单
00:15:23.833 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：7099b36df33945179a983eb28cdf2056
00:15:23.833 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：a8e0c68e6e4298a2794a51a830d5a4de
00:15:23.834 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用计量设备总台账,注册接口：8cdab9b4c78f32a3f3c9e6a9c80614a7
00:15:23.834 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用出货检验,注册视图：出货检验项目（视图）
00:15:23.835 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用检测流程,注册模型：结果汇总表单
00:15:23.836 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：b2b5915b566b3d0d8a3792d5225afa31
00:15:23.836 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用出货检验,注册视图：出货检查
00:15:23.836 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用计量设备总台账,注册接口：6b544934104f4003a3ce329a4c437632
00:15:23.838 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备管理,注册视图：巡检保修表单
00:15:23.838 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 物料类型 应用Id: 76457baaf9754281d3f037e1f611cff9
00:15:23.838 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用计量设备总台账,注册接口：79382454009d487a819c7c51f363bf39
00:15:23.839 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 报告撤换 应用Id: c213aeb7bb7a6931eb3d13058812fb63
00:15:23.839 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：6a8f6b15b1924e9298c5818e7861d9dc
00:15:23.839 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料类型,注册视图：物料类型
00:15:23.839 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用报告撤换,注册视图：报告撤换表单
00:15:23.840 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：d2b5b3e6b9dc49f2a5a2a161106db384
00:15:23.841 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用计量设备总台账,注册模型：台账管理表单
00:15:23.842 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用出货检验,注册视图：出货检查（视图）
00:15:23.843 [ForkJoinPool.commonPool-worker-13] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用计量设备总台账,注册模型：设备类型表单
00:15:23.843 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用出货检验,注册接口：8404cb00a11f4d96be48fbd48f72de99
00:15:23.843 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：53d0171fa67f429953fc4e089f8eecf3
00:15:23.843 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：8e5417e5924c4f60bf527d183048cd62
00:15:23.845 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品作废列表
00:15:23.845 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：9802d96bb8b8cd62ccb5fb098ea9b6f7
00:15:23.846 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：000e6c8baa6133c57c3246555517d31a
00:15:23.846 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：9106104593f5788c0d0011a0dd77a1ce
00:15:23.847 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用出货检验,注册接口：b755c1a46e924abfb013090c3f22c7af
00:15:23.848 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：4ef677855b85fda50e27525410bea550
00:15:23.848 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：0de566b76f3349968b578fc9b8a7096f
00:15:23.848 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备管理,注册视图：设备报废表单
00:15:23.849 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：b71c0cb9368727e0587301a9d78a10a6
00:15:23.849 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：909fca60387660598dc22188279b735f
00:15:23.850 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用标准物质管理,注册接口：92ad21a1752a52c3e6aa035dece89041
00:15:23.851 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：62d406a7be909d20608c27ccd825455a
00:15:23.852 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用出货检验,注册接口：7cdad73bc921404c86ec8258cdadc16e
00:15:23.852 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用标准物质管理,注册模型：标准滴定溶液(BY)基础配置表单
00:15:23.853 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：f3dabdd17bae49f88683a0ddab1f6d25
00:15:23.853 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：ddffb336e2eff20b8e0dec3a8b361135
00:15:23.854 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用标准物质管理,注册模型：基准物质配置表单(基础配置)
00:15:23.855 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用标准物质管理,注册模型：标定公式配置表单
00:15:23.855 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：da2c8c02b09e7a02ed37120fd5b134a4
00:15:23.856 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用报告撤换,注册视图：报告撤换列表
00:15:23.856 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料类型,注册接口：bc4834df2743f88a9f70f74c8caeb0a3
00:15:23.856 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用出货检验,注册模型：出货检验项目
00:15:23.856 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$8,206] - 注册应用原合同清单,注册定时任务：测试1
00:15:23.857 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报告撤换,注册接口：37091bef157fe0dbceb063ec225935b3
00:15:23.857 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：8e61ca4568c745a99c8a0eda68462f45
00:15:23.857 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用标准物质管理,注册模型：主成分详情
00:15:23.858 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用出货检验,注册模型：检验项目
00:15:23.858 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：f8950a92b105c370db26dd13d48434e0
00:15:23.859 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报告撤换,注册接口：98ce948564e7a61e6cb9bd6f3eaa4287
00:15:23.859 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：78cc693d4d743b40e90f896cfd66afbf
00:15:23.860 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用标准物质管理,注册模型：标准物质台账表单
00:15:23.860 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用出货检验,注册模型：出货检查
00:15:23.861 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：5b5f17d3e493b899719aa6a620080ed6
00:15:23.861 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报告撤换,注册接口：94ee8402ab5babdba36c00c86178a53a
00:15:23.862 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：58594ca6912c4281afd782bd815bac14
00:15:23.864 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：f1ac506907a8f93f88a9004730cc3a13
00:15:23.864 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报告撤换,注册接口：676e96728936f4b2dbba33b86cba378c
00:15:23.864 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用标准物质管理,注册模型：基准溶液(JZ)配置表单
00:15:23.868 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：5df8a676b75df54886bd919b3be81736
00:15:23.868 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用报告撤换,注册模型：报告撤换表单
00:15:23.869 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：e96646cd31d54b32beeee8e6dfea3c47
00:15:23.870 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：763d3004e391d032529819ad4827cd3f
00:15:23.871 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用标准物质管理,注册模型：标准物质基础配置表单
00:15:23.871 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：65346a500ff5217bed5dcb6f05955607
00:15:23.872 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：e3b51ad6649344b58e418669852eae76
00:15:23.872 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用标准物质管理,注册模型：标准滴定溶液(BY)配置表单(主任务发布)
00:15:23.875 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：328c297aac636f0406ff70d1a330093c
00:15:23.877 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用文件管理,注册接口：15d0c181c8b34bbeb9cc6e202cc83a8e
00:15:23.878 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用标准物质管理,注册模型：RM_List
00:15:23.879 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$9,223] - 注册应用原合同清单,注册条码：asd
00:15:23.880 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：77c97b3945ee891f785deb142fd54839
00:15:23.880 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 报表测试20250206 应用Id: 13d02eec77f4e799fc7f49b2929b98a5
00:15:23.881 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用报表测试20250206,注册视图：测试20250206
00:15:23.881 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备管理,注册接口：d60654e5e9094aaef43159f85e5f5394
00:15:23.881 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用标准物质管理,注册模型：CRM_List
00:15:23.881 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：ca636588e27c0addb2e906c4b85a94f0
00:15:23.881 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用文件管理,注册模型：文件管理
00:15:23.883 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备管理,注册接口：0293f7048e5a6dc8fe2de0172a13348c
00:15:23.883 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：eb341a2dc1f79b24379a8a5615a3c33b
00:15:23.883 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用标准物质管理,注册模型：标准滴定溶液(BY)标定（子任务领取）
00:15:23.883 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用文件管理,注册模型：文件类型
00:15:23.884 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备管理,注册接口：78c02b344c6acaaf3a2d60ce36a0e266
00:15:23.885 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用文件管理,注册流程：null
00:15:23.885 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报表测试20250206,注册接口：da058c87c9284ce58dfa1e9721893ca7
00:15:23.885 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：404bd78a57c3fb4c7e7d2d41159a24c4
00:15:23.886 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备管理,注册接口：f9e5b9c9df50883160094c83a1a4db40
00:15:23.886 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：7c77b4ddc0e6e594de6f34944a6d06b0
00:15:23.887 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报表测试20250206,注册接口：f0c98e64b73d4f30a7d297b314a070a5
00:15:23.887 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备管理,注册接口：acf926bdc6500d01ded009c70e8c9525
00:15:23.887 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用标准物质管理,注册触发器：根据台账新增数量录入对应数据
00:15:23.888 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：1c8d1d6a2d771c0d1f60e462bdbcddb2
00:15:23.888 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 记录模板管理 应用Id: 2ee202ffa35bbab0cf2a34308bbec92b
00:15:23.888 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备管理,注册接口：6ab06c528730a56c836ffd8ead58b89b
00:15:23.888 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用记录模板管理,注册视图：记录模板管理列表
00:15:23.889 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报表测试20250206,注册接口：cbc40965e12b44f0932d692bb5d4a417
00:15:23.889 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：6da8f4aac40992216bf35351ff278120
00:15:23.889 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用记录模板管理,注册视图：记录模板管理表单
00:15:23.890 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备管理,注册接口：1fc82bd16e9b42b0b548247236ab958a
00:15:23.890 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报表测试20250206,注册接口：4166b358c16648d3abaa174233d3efd7
00:15:23.891 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备管理,注册接口：c845c2b9ea39d6bd097b415121cf1655
00:15:23.891 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：461c51e8c9468b41b651741dee4eb79a
00:15:23.892 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用报表测试20250206,注册模型：测试20250206
00:15:23.892 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备管理,注册接口：5ef0c57f5a0092ff8de99a80c53ba858
00:15:23.893 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：5eb60f702213ce27629f06421e8b064d
00:15:23.894 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备管理,注册接口：50745ff6ddf21d0457eda8c35f313216
00:15:23.894 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$10,235] - 注册应用报表测试20250206,注册报表：报表1
00:15:23.895 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：60fccf6045063c63acee5cf87a23400c
00:15:23.895 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备管理,注册接口：db4a09da82168afe687debabe04bd59a
00:15:23.895 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用记录模板管理,注册模型：记录模板管理表单
00:15:23.896 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备管理,注册接口：e6d2e33a37c6935e09b160c5d3ee158f
00:15:23.897 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 班组设置管理 应用Id: a4639aa41d775b2f7b29c00edae40084
00:15:23.897 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用班组设置管理,注册视图：班组设置列表
00:15:23.897 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：5265c9192a88fc92c42dc5ce6edff7b7
00:15:23.898 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备管理,注册接口：d3456fb2e8beadb4fb23a4e59ed02c32
00:15:23.898 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用班组设置管理,注册视图：班组设置表单
00:15:23.898 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$10,235] - 注册应用报表测试20250206,注册报表：检测委托协议
00:15:23.899 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备管理,注册接口：e5bec0574843d4f85bee82f9667c05f9
00:15:23.899 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$10,235] - 注册应用报表测试20250206,注册报表：合并单元格数字问题
00:15:23.900 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：327b4ab2b2d916f3acdf8f13835c89e8
00:15:23.900 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$10,235] - 注册应用报表测试20250206,注册报表：测试新增
00:15:23.901 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$10,235] - 注册应用报表测试20250206,注册报表：测试新增2
00:15:23.901 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备管理,注册接口：72ce8b617caba0ff362913d1088003ad
00:15:23.901 [ForkJoinPool.commonPool-worker-10] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用班组设置管理,注册模型：班组设置表单
00:15:23.902 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：9247bec38758804f9eef89a06673ebe2
00:15:23.902 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$10,235] - 注册应用报表测试20250206,注册报表：新20250305-1
00:15:23.902 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备管理,注册接口：46e0296c938d7fcdb4a46c36ec422eff
00:15:23.903 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：f9878c30b18a5483b289ee2913d1968f
00:15:23.903 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 报表测试20250331 应用Id: 50269b7d4de3203af173918a9965dd46
00:15:23.904 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备管理,注册接口：5debb6111ead35fc9c0879e8cc386d0a
00:15:23.904 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用报表测试20250331,注册视图：报表uid
00:15:23.904 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：c5e649418d631fbdb613042db604551e
00:15:23.905 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：a4dd07f52a9bf60abc0a6f742245a55e
00:15:23.905 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备管理,注册模型：检定计划表单
00:15:23.906 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用报表测试20250331,注册视图：列表视图
00:15:23.907 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用报表测试20250331,注册视图：pc视图
00:15:23.907 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备管理,注册模型：核查计划表单
00:15:23.907 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报表测试20250331,注册接口：eeb031e9adfd4f61b44d979ed7ce99cf
00:15:23.909 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：2489c6e67a9749bb175964d7547194e0
00:15:23.909 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用报表测试20250331,注册模型：报表uid
00:15:23.911 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$10,235] - 注册应用报表测试20250331,注册报表：报表20250331
00:15:23.911 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：危化品实时库存表单
00:15:23.912 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备管理,注册模型：设备申购表单
00:15:23.912 [ForkJoinPool.commonPool-worker-60] INFO  c.l.i.LdfBusinessConfig - [lambda$register$10,235] - 注册应用报表测试20250331,注册报表：报表测试20250401
00:15:23.912 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：班组当班库存表单
00:15:23.914 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：危化品库存日志表单
00:15:23.916 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：危化品信息表单
00:15:23.916 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备管理,注册模型：设备台账表单
00:15:23.916 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$8,206] - 注册应用文件管理,注册定时任务：作废时间提示(每小时进行一次)
00:15:23.917 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：班组库存日志表单
00:15:23.918 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备管理,注册模型：设备检定表单
00:15:23.919 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：危化品入库登记表单
00:15:23.921 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备管理,注册模型：期间核查表单
00:15:23.922 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：危化品领用明细表单
00:15:23.923 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备管理,注册模型：巡检保修表单
00:15:23.923 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：出库明细
00:15:23.925 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：危化品领用登记表单
00:15:23.926 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备管理,注册模型：设备报废表单
00:15:23.928 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：退库明细
00:15:23.929 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用设备管理,注册触发器：检定计划发布检定任务
00:15:23.929 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用设备管理,注册触发器：核查计划发起核查任务
00:15:23.930 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 预警设置 应用Id: 07e67b6663275fcd75a395355372b364
00:15:23.930 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：入库明细
00:15:23.932 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：危化品退库登记表单
00:15:23.938 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：盘点试剂
00:15:23.944 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：危化品盘点登记表单
00:15:23.945 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：61ff84dd26c51ca88de5f495c463d9ac
00:15:23.945 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用单位管理,注册接口：a17fb8492b78261b877469f5d7d05763
00:15:23.945 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料类型,注册接口：52f9348e904701651f9729bfd7ed84ae
00:15:23.945 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备基础数据管理,注册接口：c98570863f3d273431ec412e5bb8806f
00:15:23.945 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用格林美服务,注册接口：7a19ddb1526e01594f95b60366dd76bf
00:15:23.945 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：c9f518412450ce691ae0ee6037229773
00:15:23.945 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用人员管理,注册接口：f68c8d95c26cd48615e5d62573e71879
00:15:23.945 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用落尘管理,注册接口：63fe754afbf3480ebfde9bd6c8ea5ad0
00:15:23.945 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用ADMIN新增业务,注册模型：测试编码生成器
00:15:23.945 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用仪表配置,注册接口：d2c8ecfd4d537f923956197ba4f55563
00:15:23.947 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料类型,注册接口：4fcf5278919244386b6abe8d945c78cc
00:15:23.947 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用人员管理,注册接口：759710015998605bea164cec6996a44c
00:15:23.948 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用仪表配置,注册接口：8d328946e5f57ed491d829d18858a4e1
00:15:23.950 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用人员管理,注册接口：69230e1d58f0d5c92785539faf1f48ad
00:15:23.951 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：交班明细
00:15:23.951 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用ADMIN新增业务,注册模型：测试编码生成2
00:15:23.951 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：f042e78da47497e0484c1fbb70f9c521
00:15:23.951 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用仪表配置,注册接口：3da40eeb75083a69477e009938887e8c
00:15:23.951 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备基础数据管理,注册接口：215bd8b02c80072ed1e5884e48bf6904
00:15:23.951 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用单位管理,注册接口：e0f5234cbec6436c6122220acec7b35f
00:15:23.952 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用格林美服务,注册接口：d655ce1744c5b8f297a2665c3f1d7d03
00:15:23.952 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：c6f91a147b83a49de951b962d4a8e951
00:15:23.952 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用人员管理,注册接口：9294057859eab6e68eb6ca3d661e4e6a
00:15:23.953 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料类型,注册接口：18621de3cea7b2540e8e0a33dadd532d
00:15:23.953 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用仪表配置,注册接口：8c144558e44fefecdddc576b6251cf42
00:15:23.953 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：接班明细
00:15:23.953 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用设备基础数据管理,注册接口：f7f2c7ed76784625be6301042feecf4c
00:15:23.954 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用落尘管理,注册接口：ee917b1e1c32471792cb257cbd316db2
00:15:23.955 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用ADMIN新增业务,注册模型：cq
00:15:23.955 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用人员管理,注册接口：5ae0dfa8eeb4ef51c212ab1b78c51663
00:15:23.955 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用仪表配置,注册接口：0f4c205495839feaeef62ccf8a59dce4
00:15:23.956 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用单位管理,注册接口：1fbceeb9b5b9a03691e1bbee5cba7676
00:15:23.956 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：交接班管理表单
00:15:23.956 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：9bd3306a25e982b0c7d69ef05d400dc8
00:15:23.956 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：c4c495fab23665d153e999f51f161452
00:15:23.956 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用格林美服务,注册接口：1973f483ee78a68c7d68c5b5dd0653fa
00:15:23.956 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用人员管理,注册接口：147f8cd5cdb88e3f95e8f5a7b937459f
00:15:23.956 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 三相电流 应用Id: 5546b65562cb6911d40b2185dddd4f6d
00:15:23.957 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用仪表配置,注册模型：仪表供应商
00:15:23.957 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用三相电流,注册视图：电流
00:15:23.957 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料类型,注册接口：62763a9e13f3b8644e0ae9d4aa87e7d1
00:15:23.957 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用格林美服务,注册接口：55d5b0f8bd77a69ffdcb4fab8885ac50
00:15:23.958 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用人员管理,注册接口：66070e23f0357219ac70de38989e09ec
00:15:23.958 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备基础数据管理,注册模型：设备类型列表
00:15:23.958 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用仪表配置,注册模型：仪表区域
00:15:23.958 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用落尘管理,注册接口：70a128e5dbb0447ba8358db9befac09b
00:15:23.960 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用单位管理,注册接口：c9384c35ae9af250a01bb74577e50c5d
00:15:23.960 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用人员管理,注册接口：9bae4c987a7099490ca955ebc01e0a75
00:15:23.960 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：作废明细
00:15:23.961 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备基础数据管理,注册模型：设备类型表单
00:15:23.961 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：be1538b4851ba14c02af6b749782d4fa
00:15:23.961 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：0e1ae946dfeddaeb2a416b824d333d9e
00:15:23.961 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料类型,注册接口：6bdbe3b6fd6be393a706bfdfa4ef2354
00:15:23.962 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用人员管理,注册模型：人员能力确认表单
00:15:23.962 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备基础数据管理,注册模型：设备区域表单
00:15:23.963 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用格林美服务,注册接口：00dc6e3d12e948af11b368ba7ff9a69f
00:15:23.963 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：危化品作废表单
00:15:23.963 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用仪表配置,注册模型：能耗仪表
00:15:23.963 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用人员管理,注册模型：人员基本信息表单
00:15:23.963 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备基础数据管理,注册模型：厂家信息表单
00:15:23.964 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用落尘管理,注册接口：6646910c2b2e41a18345d78f233febd9
00:15:23.964 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 物料属性 应用Id: c3d54939c2bad7ddea835d7230585db6
00:15:23.965 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料属性,注册视图：物料属性
00:15:23.966 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备基础数据管理,注册模型：产线表单
00:15:23.966 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用三相电流,注册视图：dasda
00:15:23.966 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用仪表配置,注册模型：区域结构配置
00:15:23.966 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用人员管理,注册模型：人员岗位培训表单
00:15:23.966 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：d288a1af4e169e6afcee3b452dad230c
00:15:23.966 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用三相电流,注册视图：首页
00:15:23.967 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：345f78ea2226c55e09b53978a2a3fefa
00:15:23.967 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料类型,注册接口：cd19102a77fe930769ad0c148703f70b
00:15:23.967 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备基础数据管理,注册模型：模板名称表单
00:15:23.967 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用仪表配置,注册模型：计算规则列表
00:15:23.968 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用格林美服务,注册接口：4f29c8dcd7ce3485011ee2e4dbc32556
00:15:23.968 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用落尘管理,注册接口：09250c1e27ac494d934ad6d91670f8dd
00:15:23.969 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料类型,注册接口：5d054a3df10858dea2aff2d5373dfaab
00:15:23.970 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：9cc0911eae1fcc4c11e6f614e87eeadc
00:15:23.970 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用三相电流,注册模型：电流
00:15:23.970 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用仪表配置,注册模型：计算规则
00:15:23.970 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：3f425f0e8c1ca9d86141f19ec0eabbe5
00:15:23.971 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用三相电流,注册模型：dasda
00:15:23.971 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用格林美服务,注册接口：d7d804418c67b6883edae4f568b24f09
00:15:23.971 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用仪表配置,注册模型：尖峰平谷配置
00:15:23.972 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料类型,注册接口：b95ccfbf51db231041ab9c6b370d1356
00:15:23.972 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用落尘管理,注册接口：65707eebebd9425080b545732f5d4535
00:15:23.973 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：a73f7685677bc9a180e539cade24d710
00:15:23.973 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：7d119321882ea2acd46e2f9ee3a12bee
00:15:23.974 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用格林美服务,注册接口：15ccdf3f81d6bb1734164126f7154272
00:15:23.974 [ForkJoinPool.commonPool-worker-55] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料类型,注册接口：60c75f4fd2fc54d74fe723b2aba2caa0
00:15:23.974 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备基础数据管理,注册模型：巡检表单
00:15:23.974 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用落尘管理,注册接口：784b38480d604325ab911a01e134e8b4
00:15:23.976 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用落尘管理,注册接口：f07b415d94e14eb8b07e6ef00bfa033b
00:15:23.976 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：500d252066f6887e37efa43dbc6bf0e6
00:15:23.976 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备基础数据管理,注册模型：巡检表单
00:15:23.977 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：c15e76dd71e0b99b685e4f6c2cd458cc
00:15:23.977 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用落尘管理,注册接口：116a051cdbea4bb699c4a86f5dda9c6d
00:15:23.977 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用格林美服务,注册接口：fd4ffa6644d1a9b0fac43a53f8058749
00:15:23.978 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 期间核查 应用Id: 7dd8fe03ec82e902177a4ba25190ea23
00:15:23.979 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用落尘管理,注册接口：65ea6dfc2dcd4f7abc1ac6b409d7902f
00:15:23.980 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：1f2b93aca1250db17037c09c112b4de4
00:15:23.980 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用落尘管理,注册接口：e1f95a4d87924b23954391b79222d20d
00:15:23.980 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：1d5b3ab52faf2c9971b749c252266d5f
00:15:23.981 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用格林美服务,注册模型：库存列表
00:15:23.981 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性,注册接口：5d125ae666d9af75d56b7115a6c80445
00:15:23.982 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用格林美服务,注册模型：锁定列表
00:15:23.983 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：a3b4ab7250e5236fc1f88ee5c3d8a10a
00:15:23.983 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用格林美服务,注册模型：采购单列表
00:15:23.983 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：6668a35ad472190d37f179c6e45938b8
00:15:23.984 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性,注册接口：b6cdfa63827fd21ebe9dc78e7074ea3c
00:15:23.985 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用格林美服务,注册模型：订单列表
00:15:23.985 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用落尘管理,注册接口：38cfedb1f11f4dee82eaaf4da336177e
00:15:23.986 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用格林美服务,注册触发器：监听【采购服务.新增】
00:15:23.986 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：69c4d5c70e37c6a018724b67835e5052
00:15:23.987 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性,注册接口：467dfd53f5382ba5452ee6a62461c8dc
00:15:23.988 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用落尘管理,注册模型：落尘点位
00:15:23.989 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用落尘管理,注册模型：落尘检测元素
00:15:23.989 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：c94e43886ce6392168c1e09ce58c7d87
00:15:23.990 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用落尘管理,注册模型：落尘检验单关联取样信息表
00:15:23.991 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性,注册接口：86e937523ee69ae25e18e372bf66b78d
00:15:23.992 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：4943f5e7238a28201f47a87d505fbfd2
00:15:23.993 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性,注册接口：239cceea250bbc77fad248fd9b321a37
00:15:23.994 [main] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 检验单 应用Id: b4a347ddee367619986eccce621b3f9d
00:15:23.995 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 产品BOM 应用Id: b3a21e3d6d4838aa0932b7e8b0890fdd
00:15:23.995 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检验单,注册视图：质量管理-检验单-检验单
00:15:23.996 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用产品BOM,注册视图：产品BOM
00:15:23.996 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性,注册接口：96e44ef16b852c12efc58fcdaa62d4ae
00:15:24.000 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 物料分类 应用Id: 51051f141bde35a2e41073aa2171a40d
00:15:24.000 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料分类,注册视图：物料分类
00:15:24.009 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：7dcdb1a4306836f5bf48f2e478c0249e
00:15:24.012 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：4df981647974fcaa997d300aaafeda74
00:15:24.015 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：b6a063e269bc1319ade1af7b76043544
00:15:24.017 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用产品BOM,注册视图：产品BOM变更
00:15:24.017 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：450adff4f68af1a85c544feda53bc459
00:15:24.020 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：49d607121cbcdad8be12118526554ac1
00:15:24.022 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：e2e1b1e76617abdd9498f0155c3da075
00:15:24.024 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：92555d17a476c394c2cb211e4fa7d6ce
00:15:24.024 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检验单,注册接口：da72c43a444d45299b88bc65cea2ea56
00:15:24.025 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：935b9b19acb0175eadfca0a23d3a6909
00:15:24.027 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检验单,注册接口：5a668fe59e554a0bae02e5527bc89b58
00:15:24.028 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：feb3135747fa9d110316926c0b88820f
00:15:24.028 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：5261a8eb9cb41edc457fb879f8bf61b2
00:15:24.030 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检验单,注册接口：031ee68df8164c0b95e5584ab294d5ea
00:15:24.032 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：d0114ca7f7580a6a490b6ac903bb3f52
00:15:24.032 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：6472bac831943aa59464d5af7cf1c484
00:15:24.033 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检验单,注册接口：7519864bc03143878689864eaa2d699c
00:15:24.035 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：2577ea7b00dbbfa567e243a0d04b6bab
00:15:24.035 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：bdbb6e44e710cacb0f152d99c9abf421
00:15:24.037 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检验单,注册接口：e689ff3610bc45689cf288ebd22d645d
00:15:24.037 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：6af204515c50d5b5e41670b87c3dfcbf
00:15:24.038 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：f29a40fec98a8d20d0fb45a256131ce1
00:15:24.040 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：f99a8f8d3b54846d92d3de1d23920aae
00:15:24.040 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：3e47b8a9e702b43bba1d19cc2f8a50ac
00:15:24.040 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检验单,注册接口：caa119a9ca854c4a85479885f5065116
00:15:24.043 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：8c60532731f8ed1597d6ff77be8208cc
00:15:24.043 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：071b4e8d4beb68b1b6fa857d698691e8
00:15:24.044 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检验单,注册接口：259639cf516946ce9ba27a33d32f89ab
00:15:24.046 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：0f2ce3fea8296e12b63c3a62091a1bf0
00:15:24.047 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检验单,注册接口：09df3ab796c84044a17f3a1ed98049ee
00:15:24.049 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：b0f00886d67375eaf64de4b1b62278fb
00:15:24.050 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检验单,注册接口：32eafa49f45d455fa388ed001192fd88
00:15:24.052 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：f591ca76187e85bc6465e9fd7034721f
00:15:24.052 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检验单,注册接口：b62b0a94355148dea0266af3c15c2421
00:15:24.053 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检验单,注册接口：d4def4d6cc8c4214a9ac7cfa9b2e1072
00:15:24.054 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：024e5af56244918a2da8b21f9bb78262
00:15:24.054 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用检验单,注册模型：检验单详情
00:15:24.056 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用检验单,注册模型：质量管理-检验单-检验单
00:15:24.057 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：8b0fecfa53c9493b812e5c57cfbbe8d8
00:15:24.060 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：37ba14192538a23e847ab8a0294a17a6
00:15:24.060 [main] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 三害两恶台账 应用Id: 743c02f11b311a13b585653631b5765f
00:15:24.061 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用三害两恶台账,注册视图：三害两恶台账
00:15:24.063 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：7d40561ea9bc26f0814074d685b389d1
00:15:24.066 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：c4be8e00bef7fec983ebd02626863e44
00:15:24.068 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用三害两恶台账,注册视图：三害两恶台账视图
00:15:24.069 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：9ce442f0ae1c649f5e4bf77acc85fbd7
00:15:24.070 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用三害两恶台账,注册接口：61d1aaa9fd4b4ecab1ce9773e586cced
00:15:24.071 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：7d41bda1e0b613b5e9facb705341bf7b
00:15:24.072 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用三害两恶台账,注册接口：e7a60213dfc743528d2a45982a991060
00:15:24.074 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：ffdd310dfbd17a96e1dc12b20b6e6e50
00:15:24.075 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用三害两恶台账,注册接口：da9e8d445b054fd18b90c2542ad3e831
00:15:24.077 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用三害两恶台账,注册接口：10d3077c584f4ff9acdcbb08c480a979
00:15:24.078 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：41d74b8dc75152f0e260e1bae7dfd0e2
00:15:24.079 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：508a241dc27e23a4a59083dbf2017bca
00:15:24.080 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用产品BOM,注册接口：3e0313eca131ac4237b58089484e7d69
00:15:24.080 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用三害两恶台账,注册接口：9ac7ac1da0af46b2a4e859d3f68eb489
00:15:24.082 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用产品BOM,注册模型：产品BOM变更
00:15:24.083 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用三害两恶台账,注册接口：67a2511763ac4fcab70a9b4190036c65
00:15:24.083 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default-dev应用信息, 应用名称: 字典下拉 应用Id: 2eb9bc8b39943a0fc03109c5901275a1
00:15:24.083 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用字典下拉,注册接口：ca822707bc57dad41f42feeb0918cea7
00:15:24.085 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用三害两恶台账,注册模型：三害两恶台账
00:15:24.085 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用字典下拉,注册接口：abb352908f6a9e322759173543d24aad
00:15:24.087 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用字典下拉,注册接口：23b78f9376e4b46d658a52f8d971b3f9
00:15:24.089 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用字典下拉,注册接口：73ce1c8035be3f10e3cdd24f0dacdb8e
00:15:24.091 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用字典下拉,注册接口：ff7a740d386f976136de1949aa2a506c
00:15:24.092 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用字典下拉,注册接口：5f468440860b0a5845f7d18ab070722c
00:15:24.093 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用字典下拉,注册接口：b7e3bbef0b0b51ae91415d8dee9303f8
00:15:24.096 [main] INFO  c.l.i.c.LdfBusinessInfoConfig - [loadDevelopApplication,275] - 默认环境工作区应用加载完毕！
00:15:24.096 [main] INFO  c.l.i.c.LdfBusinessInfoConfig - [loadProductApplication,283] - 加载default环境应用
00:15:24.360 [ForkJoinPool.commonPool-worker-15] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 单位管理 应用Id: 9a1fcefdb2c0eed1150b736fa6c29296
00:15:24.361 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 班组设置管理 应用Id: a4639aa41d775b2f7b29c00edae40084
00:15:24.360 [main] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 记录模板管理 应用Id: 2ee202ffa35bbab0cf2a34308bbec92b
00:15:24.361 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 检测方法管理 应用Id: 0271612ba6f8491af3e68c916bafac39
00:15:24.361 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 样品类型管理 应用Id: f1983436253625c6a167d411fc2721e1
00:15:24.360 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 字典下拉 应用Id: 2eb9bc8b39943a0fc03109c5901275a1
00:15:24.361 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 危化品仓管理 应用Id: 6bc8f242ee53159bb051a10fdc246f55
00:15:24.361 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 平台BUG 应用Id: c791d848a001970d78ac6bba41acda8b
00:15:24.360 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 物料属性 应用Id: c3d54939c2bad7ddea835d7230585db6
00:15:24.361 [ForkJoinPool.commonPool-worker-62] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 测试业务 应用Id: 7be0519123db41ede8f4ce3673c204a7
00:15:24.361 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 用来测试的 应用Id: 1e7fe90ba493e543740fa45825843aba
00:15:24.362 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用样品类型管理,注册视图：样品类型管理列表
00:15:24.360 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 质控样管理 应用Id: 91d7d07c75cf856e44bde93133a89683
00:15:24.361 [ForkJoinPool.commonPool-worker-39] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 项目 应用Id: a702150952dffcfa6b96cb40d60b730a
00:15:24.362 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用班组设置管理,注册视图：班组设置列表
00:15:24.362 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检测方法管理,注册视图：检测方法管理列表
00:15:24.362 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 物料类型 应用Id: 181e29f2ab34d8eee3a33cc3961e9a98
00:15:24.362 [ForkJoinPool.commonPool-worker-62] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试业务,注册视图：测试流程
00:15:24.362 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 原合同清单 应用Id: 02461dbaf7052749e0dfbecaa6221f40
00:15:24.362 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用平台BUG,注册视图：问题反馈
00:15:24.362 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用质控样管理,注册视图：质控样列表
00:15:24.362 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：简单的流程
00:15:24.362 [ForkJoinPool.commonPool-worker-39] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用项目,注册视图：项目管理界面
00:15:24.362 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料类型,注册视图：物料类型列表
00:15:24.362 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用样品类型管理,注册视图：样品类型管理表单
00:15:24.362 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用班组设置管理,注册视图：班组设置表单
00:15:24.363 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检测方法管理,注册视图：检测方法管理表单
00:15:24.365 [ForkJoinPool.commonPool-worker-15] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用单位管理,注册视图：单位视图管理
00:15:24.365 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用字典下拉,注册接口：ca822707bc57dad41f42feeb0918cea7
00:15:24.365 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用记录模板管理,注册视图：记录模板管理列表
00:15:24.365 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料属性,注册视图：物料属性
00:15:24.365 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品信息列表
00:15:24.369 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原合同清单,注册视图：测试流程
00:15:24.369 [ForkJoinPool.commonPool-worker-62] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试业务,注册视图：测试模型
00:15:24.370 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 设备台账 应用Id: 1da8861973c48276c74d95d605cfc8ac
00:15:24.370 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 简单审批流 应用Id: 21722f7b28caa6ad09690021200b9b83
00:15:24.370 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 物料类型 应用Id: 76457baaf9754281d3f037e1f611cff9
00:15:24.370 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 客户 应用Id: 4dff6ff27e22c634acf80c737d07cf4e
00:15:24.370 [ForkJoinPool.commonPool-worker-57] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 测试引用应用 应用Id: 6aad07eae6d58b305af55371aceed9e2
00:15:24.371 [ForkJoinPool.commonPool-worker-50] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 检测依据管理 应用Id: 1fd25061fd6d9902cee29d333c130691
00:15:24.371 [ForkJoinPool.commonPool-worker-12] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 车间 应用Id: 9f137410ecc68cfceab2d4c63b14019c
00:15:24.371 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 物料属性分类 应用Id: 3fa1d145339bc4b8f809ea805731f546
00:15:24.371 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用质控样管理,注册视图：质控样表单
00:15:24.371 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用记录模板管理,注册视图：记录模板管理表单
00:15:24.371 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原合同清单,注册视图：简单流程
00:15:24.371 [ForkJoinPool.commonPool-worker-50] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检测依据管理,注册视图：检测依据列表
00:15:24.371 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：测试模型管理
00:15:24.372 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品实时库存列表
00:15:24.372 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 外部字典数据应用 应用Id: 394e402ca5254786998a176ddc27ff5f
00:15:24.372 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用客户,注册视图：客户视图管理
00:15:24.371 [ForkJoinPool.commonPool-worker-15] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用单位管理,注册视图：单位
00:15:24.371 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料类型,注册视图：物料类型
00:15:24.373 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备台账,注册视图：设备台账列表
00:15:24.373 [ForkJoinPool.commonPool-worker-12] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用车间,注册视图：车间管理
00:15:24.373 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 报告权限管理 应用Id: 022609ca997aef5fec87c31d5a2ecf8b
00:15:24.373 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 单位管理 应用Id: 67ef199cfc5762c59584d07ed8f2b560
00:15:24.374 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 物料基础配置 应用Id: 925dfd5a99fe1c1dbd4f85a86f2eec20
00:15:24.374 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用外部字典数据应用,注册视图：主表
00:15:24.374 [ForkJoinPool.commonPool-worker-50] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检测依据管理,注册视图：检测依据表单
00:15:24.374 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 物料管理 应用Id: 178dfdff277fed675c9cdc20793a5954
00:15:24.374 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用客户,注册视图：客户
00:15:24.374 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品库存日志列表
00:15:24.374 [ForkJoinPool.commonPool-worker-51] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 化验岗位管理 应用Id: 5df48a2b2a2886e1b024374b57cf29c6
00:15:24.374 [ForkJoinPool.commonPool-worker-12] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用车间,注册视图：车间
00:15:24.371 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料类型,注册视图：物料类型表单
00:15:24.374 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用单位管理,注册接口：0b4d881cae9901cdb26e4408cdf23cf6
00:15:24.374 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 物料分类 应用Id: 51051f141bde35a2e41073aa2171a40d
00:15:24.375 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料属性分类,注册视图：物料属性分类
00:15:24.375 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 品牌 应用Id: 807430741964b3e68775afad4591e22b
00:15:24.371 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用简单审批流,注册视图：简易流程提交界面
00:15:24.378 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用报告权限管理,注册视图：报告权限管理列表
00:15:24.381 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料管理,注册视图：物料管理
00:15:24.381 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用设备台账,注册视图：设备台账表单
00:15:24.382 [ForkJoinPool.commonPool-worker-30] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 仓库设置 应用Id: 088693ad69df5c7bfda3421f084395da
00:15:24.382 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料基础配置,注册视图：物料基础信息列表
00:15:24.371 [ForkJoinPool.commonPool-worker-39] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用项目,注册视图：项目
00:15:24.385 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料分类,注册视图：物料分类
00:15:24.385 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用外部字典数据应用,注册视图：子表
00:15:24.385 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品入库登记列表
00:15:24.387 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：测试模型
00:15:24.372 [ForkJoinPool.commonPool-worker-57] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试引用应用,注册视图：popo
00:15:24.372 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 计算公式管理 应用Id: 1c62b48bc35cbd429741e7e4ced57d4b
00:15:24.374 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原合同清单,注册视图：five_origina_contract
00:15:24.389 [ForkJoinPool.commonPool-worker-51] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用化验岗位管理,注册视图：化验岗位列表
00:15:24.389 [ForkJoinPool.commonPool-worker-23] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 产品类型 应用Id: 318da15175cc3b881b0ec34b25461e57
00:15:24.391 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用报告权限管理,注册视图：报告权限管理表单
00:15:24.391 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用品牌,注册视图：品牌视图管理
00:15:24.393 [ForkJoinPool.commonPool-worker-30] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仓库设置,注册视图：货架管理列表
00:15:24.393 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用简单审批流,注册视图：简易流程提交界面
00:15:24.393 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料基础配置,注册视图：物料基础信息表单
00:15:24.397 [ForkJoinPool.commonPool-worker-23] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用产品类型,注册视图：产品类型管理
00:15:24.397 [ForkJoinPool.commonPool-worker-51] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用化验岗位管理,注册视图：化验岗位表单
00:15:24.397 [ForkJoinPool.commonPool-worker-16] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 样品名称管理 应用Id: b4d167cb4e294f7ab17202dc7194c0f5
00:15:24.397 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品盘点登记列表
00:15:24.401 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用计算公式管理,注册视图：计算公式管理列表
00:15:24.403 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用班组设置管理,注册模型：班组设置表单
00:15:24.404 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 项目类别管理 应用Id: 289a18b37e7ec649346d252f331a38a2
00:15:24.404 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用品牌,注册视图：品牌
00:15:24.409 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原合同清单,注册视图：原合同清单
00:15:24.413 [ForkJoinPool.commonPool-worker-30] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仓库设置,注册视图：仓库管理列表
00:15:24.417 [ForkJoinPool.commonPool-worker-16] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用样品名称管理,注册视图：样品名称管理列表
00:15:24.417 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品领用登记列表
00:15:24.421 [ForkJoinPool.commonPool-worker-23] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用产品类型,注册视图：产品类型
00:15:24.421 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 仓库管理 应用Id: 9f422241fde4b8ca0610c0aa03a987fd
00:15:24.425 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用计算公式管理,注册视图：计算公式管理表单
00:15:24.425 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用项目类别管理,注册视图：项目类别列表
00:15:24.426 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 物料仓管理 应用Id: 9ece866d2cc676499c3a0bdaab503f59
00:15:24.427 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 检测项目管理 应用Id: 4135231aefaef62f9fb530863cdbcff3
00:15:24.428 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用外部字典数据应用,注册视图：主表
00:15:24.429 [ForkJoinPool.commonPool-worker-30] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仓库设置,注册视图：货架管理表单
00:15:24.430 [ForkJoinPool.commonPool-worker-16] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用样品名称管理,注册视图：样品名称管理表单
00:15:24.429 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 预警设置 应用Id: 07e67b6663275fcd75a395355372b364
00:15:24.430 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仓库管理,注册视图：仓库管理列表
00:15:24.431 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：班组当班库存列表
00:15:24.431 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 委托单位管理 应用Id: f5bff7a82812f248832defa7e17339fc
00:15:24.431 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/dictSelect/bomType already exists.
00:15:24.431 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用字典下拉,注册接口：abb352908f6a9e322759173543d24aad
00:15:24.431 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户default应用信息, 应用名称: 货架管理 应用Id: 86b3167eaf2795612819ef26bf6a71b0
00:15:24.431 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用项目类别管理,注册视图：项目类别管理表单
00:15:24.431 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料库存列表
00:15:24.432 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检测项目管理,注册视图：检测项目管理列表
00:15:24.432 [ForkJoinPool.commonPool-worker-15] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用单位管理,注册模型：单位
00:15:24.432 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用预警设置,注册视图：物料预警设置列表
00:15:24.432 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仓库管理,注册视图：仓库管理表单
00:15:24.433 [ForkJoinPool.commonPool-worker-57] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试引用应用,注册视图：sss
00:15:24.433 [ForkJoinPool.commonPool-worker-12] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用车间,注册接口：6477e9602c7ee96c1f7bc4b521cb3582
00:15:24.434 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用委托单位管理,注册视图：委托单位管理列表
00:15:24.434 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用货架管理,注册视图：货架管理列表
00:15:24.434 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：班组库存日志列表
00:15:24.434 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料预警列表
00:15:24.434 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/unitManage/unitSelect already exists.
00:15:24.434 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用检测项目管理,注册视图：检测项目管理表单
00:15:24.435 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用单位管理,注册接口：a17fb8492b78261b877469f5d7d05763
00:15:24.435 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用预警设置,注册视图：物料预警设置表单
00:15:24.435 [ForkJoinPool.commonPool-worker-62] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用测试业务,注册视图：测试流程
00:15:24.435 [ForkJoinPool.commonPool-worker-28] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料类型,注册模型：物料类型表单
00:15:24.435 [ForkJoinPool.commonPool-worker-57] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试引用应用,注册模型：popo
00:15:24.435 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用货架管理,注册视图：货架管理表单
00:15:24.435 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用委托单位管理,注册视图：委托单位管理表单
00:15:24.436 [ForkJoinPool.commonPool-worker-39] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用项目,注册模型：项目
00:15:24.436 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品退库登记列表
00:15:24.436 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料信息列表
00:15:24.437 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用质控样管理,注册接口：6c4c47bb4b5b725623ec0c3570b2626c
00:15:24.437 [ForkJoinPool.commonPool-worker-37] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用品牌,注册模型：品牌
00:15:24.437 [ForkJoinPool.commonPool-worker-46] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用样品类型管理,注册模型：样品类型管理表单
00:15:24.440 [ForkJoinPool.commonPool-worker-12] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用车间,注册模型：车间
00:15:24.442 [ForkJoinPool.commonPool-worker-50] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测依据管理,注册接口：ad81e54557a2c11e086838447a3514c3
00:15:24.442 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：库存日志列表
00:15:24.442 [ForkJoinPool.commonPool-worker-23] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用产品类型,注册模型：产品类型
00:15:24.442 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：交接班管理列表
00:15:24.442 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用平台BUG,注册视图：BUG管理
00:15:24.444 [ForkJoinPool.commonPool-worker-51] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用化验岗位管理,注册视图：岗位人员管理视图
00:15:24.444 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：用户列表
00:15:24.444 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用质控样管理,注册接口：beaabc79937eb26f7a38b03974967d2a
00:15:24.444 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用记录模板管理,注册模型：记录模板管理表单
00:15:24.444 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料类型列表
00:15:24.446 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品实时库存表单
00:15:24.450 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用平台BUG,注册模型：问题反馈
00:15:24.452 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：简单的流程
00:15:24.454 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：出库记录列表
00:15:24.455 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报告权限管理,注册接口：3aab135cd3e76fe885f9dc618181b147
00:15:24.455 [ForkJoinPool.commonPool-worker-50] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用检测依据管理,注册模型：检测依据表单
00:15:24.455 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/dictSelect/bomDetailTyppe already exists.
00:15:24.456 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用字典下拉,注册接口：23b78f9376e4b46d658a52f8d971b3f9
00:15:24.456 [ForkJoinPool.commonPool-worker-1] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料基础配置,注册模型：物料基础信息表单
00:15:24.457 [ForkJoinPool.commonPool-worker-30] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用仓库设置,注册视图：仓库管理表单
00:15:24.457 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用客户,注册接口：6477e9602c7ee96c1f7bc4b521cb3582
00:15:24.457 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料盘点列表
00:15:24.457 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用质控样管理,注册接口：f54c2461260a915a3840e3b9c1cb5ce3
00:15:24.458 [ForkJoinPool.commonPool-worker-9] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用项目类别管理,注册模型：项目类别管理表单
00:15:24.458 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原合同清单,注册视图：一个新的数据模型
00:15:24.458 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/unitManage/unitPage already exists.
00:15:24.458 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用单位管理,注册接口：e0f5234cbec6436c6122220acec7b35f
00:15:24.458 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报告权限管理,注册接口：ad53f3ab011d079595c86695b2213882
00:15:24.458 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：入库记录列表
00:15:24.459 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用外部字典数据应用,注册接口：bbff5394122409728798e8f7a363ce8a
00:15:24.459 [ForkJoinPool.commonPool-worker-16] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用样品名称管理,注册接口：45fc22a1f92dd0ba32aa91226f97a3ce
00:15:24.459 [ForkJoinPool.commonPool-worker-62] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用测试业务,注册接口：f87f0168bfcb514438a2f890bcf5bad0
00:15:24.460 [ForkJoinPool.commonPool-worker-43] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用货架管理,注册模型：货架管理表单
00:15:24.460 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料信息表单
00:15:24.460 [ForkJoinPool.commonPool-worker-19] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用质控样管理,注册模型：质控样表单
00:15:24.461 [ForkJoinPool.commonPool-worker-35] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用仓库管理,注册模型：仓库管理表单
00:15:24.461 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：36f4292693867cf98110936c784aadb6
00:15:24.461 [ForkJoinPool.commonPool-worker-0] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用客户,注册模型：客户
00:15:24.461 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用简单审批流,注册接口：fa49c1af452dc642856e5bb683cbe891
00:15:24.462 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报告权限管理,注册接口：bb49978f6b76dc884b9c3fe1ef51ac50
00:15:24.462 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用计算公式管理,注册接口：da02caf0cd195e3b93225fb383a6cdde
00:15:24.462 [ForkJoinPool.commonPool-worker-52] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用检测项目管理,注册模型：检测项目管理表单
00:15:24.463 [ForkJoinPool.commonPool-worker-16] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用样品名称管理,注册模型：样品名称管理表单
00:15:24.463 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用外部字典数据应用,注册模型：子表
00:15:24.464 [ForkJoinPool.commonPool-worker-2] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用平台BUG,注册流程：null
00:15:24.465 [ForkJoinPool.commonPool-worker-62] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试业务,注册模型：data-table
00:15:24.465 [ForkJoinPool.commonPool-worker-14] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用简单审批流,注册模型：简易流程提交界面
00:15:24.465 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原合同清单,注册视图：rty
00:15:24.465 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/dictSelect/unitSelect already exists.
00:15:24.466 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用字典下拉,注册接口：73ce1c8035be3f10e3cdd24f0dacdb8e
00:15:24.466 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用计算公式管理,注册接口：543649de57ab79a76616523964e8c455
00:15:24.466 [ForkJoinPool.commonPool-worker-22] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用外部字典数据应用,注册模型：主表
00:15:24.466 [ForkJoinPool.commonPool-worker-17] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用报告权限管理,注册模型：报告权限管理表单
00:15:24.467 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原合同清单,注册视图：列表
00:15:24.467 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/unitManage/getUnitType already exists.
00:15:24.468 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用单位管理,注册接口：1fbceeb9b5b9a03691e1bbee5cba7676
00:15:24.468 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：班组当班库存表单
00:15:24.469 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原合同清单,注册视图：UI设计
00:15:24.469 [ForkJoinPool.commonPool-worker-45] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用预警设置,注册模型：物料预警设置表单
00:15:24.469 [ForkJoinPool.commonPool-worker-30] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用仓库设置,注册模型：货架管理表单
00:15:24.469 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用字典下拉,注册接口：ff7a740d386f976136de1949aa2a506c
00:15:24.469 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：7dcdb1a4306836f5bf48f2e478c0249e
00:15:24.469 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用计算公式管理,注册接口：80c664c87b6600737495722fd1050982
00:15:24.470 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/itemParameterClass/recovery already exists.
00:15:24.470 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：c9f518412450ce691ae0ee6037229773
00:15:24.471 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料类型,注册接口：bc4834df2743f88a9f70f74c8caeb0a3
00:15:24.471 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用用来测试的,注册接口：5478b2a26725469390dc033d367359df
00:15:24.471 [ForkJoinPool.commonPool-worker-29] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用计算公式管理,注册模型：计算公式管理表单
00:15:24.471 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料库存表单
00:15:24.472 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用字典下拉,注册接口：5f468440860b0a5845f7d18ab070722c
00:15:24.472 [ForkJoinPool.commonPool-worker-62] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试业务,注册模型：测试模型
00:15:24.472 [ForkJoinPool.commonPool-worker-30] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用仓库设置,注册模型：仓库管理表单
00:15:24.473 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/unitManage/changeTargetData already exists.
00:15:24.474 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用字典下拉,注册接口：b7e3bbef0b0b51ae91415d8dee9303f8
00:15:24.474 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用单位管理,注册接口：c9384c35ae9af250a01bb74577e50c5d
00:15:24.475 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/class/updateClass already exists.
00:15:24.475 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用委托单位管理,注册接口：d7b7d07b2b010188d45dba830b031e87
00:15:24.475 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：4df981647974fcaa997d300aaafeda74
00:15:24.475 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性,注册接口：5d125ae666d9af75d56b7115a6c80445
00:15:24.475 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用用来测试的,注册接口：3b8502d33e9a4adfb4e16bdc4a2077a6
00:15:24.475 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/itemParameterClass/next already exists.
00:15:24.476 [ForkJoinPool.commonPool-worker-51] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用化验岗位管理,注册接口：547f5b997f6e47a97aab908b482cd7e3
00:15:24.476 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：f042e78da47497e0484c1fbb70f9c521
00:15:24.476 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/itemType/getClassByTypeCode already exists.
00:15:24.476 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料类型,注册接口：52f9348e904701651f9729bfd7ed84ae
00:15:24.477 [ForkJoinPool.commonPool-worker-62] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用测试业务,注册模型：测试流程
00:15:24.479 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品库存日志表单
00:15:24.479 [ForkJoinPool.commonPool-worker-40] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用委托单位管理,注册模型：委托单位管理表单
00:15:24.479 [ForkJoinPool.commonPool-worker-51] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用化验岗位管理,注册接口：941357f14575709a54851ced33754f08
00:15:24.479 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用用来测试的,注册接口：ff2b1b5c1a5f4808b0cf28caba53bfc6
00:15:24.479 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料类型,注册接口：4fcf5278919244386b6abe8d945c78cc
00:15:24.480 [ForkJoinPool.commonPool-worker-41] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/unitManage/changeMainData already exists.
00:15:24.480 [ForkJoinPool.commonPool-worker-18] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/dictSelect/materialPropertiesModule already exists.
00:15:24.482 [ForkJoinPool.commonPool-worker-62] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用测试业务,注册流程：null
00:15:24.483 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用用来测试的,注册接口：82960643a77f4b67870d73b9707afd00
00:15:24.483 [ForkJoinPool.commonPool-worker-51] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用化验岗位管理,注册接口：21003387e6289d1d1dc12012694ca229
00:15:24.484 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/class/classParamsPage already exists.
00:15:24.484 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/itemParameterClass/delete already exists.
00:15:24.484 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：b6a063e269bc1319ade1af7b76043544
00:15:24.484 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：9bd3306a25e982b0c7d69ef05d400dc8
00:15:24.484 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/parameter/recovery already exists.
00:15:24.484 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性,注册接口：b6cdfa63827fd21ebe9dc78e7074ea3c
00:15:24.486 [ForkJoinPool.commonPool-worker-51] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用化验岗位管理,注册接口：30ba4d098dd99481f0edfd357cc157ab
00:15:24.489 [ForkJoinPool.commonPool-worker-62] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用测试业务,注册触发器：流程结束
00:15:24.489 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测方法管理,注册接口：6da4ec1780e05111d55e7ebbc2ee9744
00:15:24.489 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料预警表单
00:15:24.489 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/itemType/recovery already exists.
00:15:24.489 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料类型,注册接口：18621de3cea7b2540e8e0a33dadd532d
00:15:24.489 [ForkJoinPool.commonPool-worker-51] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用化验岗位管理,注册接口：4ccb792e08e1b1d59f62a33e0c561d5c
00:15:24.490 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用用来测试的,注册接口：2a0259c9dfc14db0bae6bb23b9bd2808
00:15:24.491 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测方法管理,注册接口：30355febff91564e59cd0e8dfec58f7a
00:15:24.491 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：d910a1eadaa705b0db63fc637bb0f37a
00:15:24.491 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/class/recovery already exists.
00:15:24.491 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/itemParameterClass/del already exists.
00:15:24.491 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：450adff4f68af1a85c544feda53bc459
00:15:24.491 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：be1538b4851ba14c02af6b749782d4fa
00:15:24.491 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/parameter/del already exists.
00:15:24.491 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性,注册接口：467dfd53f5382ba5452ee6a62461c8dc
00:15:24.492 [ForkJoinPool.commonPool-worker-51] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用化验岗位管理,注册接口：0664cdd045dcd6f45651004b015cb483
00:15:24.492 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用用来测试的,注册模型：测试模型
00:15:24.492 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测方法管理,注册接口：b4ee1eb1e62514a8739098b91ea4c638
00:15:24.493 [ForkJoinPool.commonPool-worker-51] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用化验岗位管理,注册接口：97b287128a8549429646c8b24a729b8a
00:15:24.493 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用用来测试的,注册模型：子表
00:15:24.493 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/itemType/state already exists.
00:15:24.494 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料类型,注册接口：62763a9e13f3b8644e0ae9d4aa87e7d1
00:15:24.494 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测方法管理,注册接口：2b53093e7842bf3d2d9e93cbfb8ad3a2
00:15:24.495 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/class/getNextArr already exists.
00:15:24.496 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：49d607121cbcdad8be12118526554ac1
00:15:24.496 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/item/getParameterListByClassOrTypeInEdit already exists.
00:15:24.496 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：61ff84dd26c51ca88de5f495c463d9ac
00:15:24.496 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/itemParameterClass/parameterClassTree already exists.
00:15:24.496 [ForkJoinPool.commonPool-worker-51] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用化验岗位管理,注册接口：62804de51a39e15134af721311ca47aa
00:15:24.496 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：345f78ea2226c55e09b53978a2a3fefa
00:15:24.496 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测方法管理,注册接口：28b5408071cf21458847bb619ad8ef94
00:15:24.497 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用用来测试的,注册模型：简单的流程
00:15:24.502 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：库存日志表单
00:15:24.502 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品信息表单
00:15:24.502 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/parameter/page already exists.
00:15:24.502 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性,注册接口：86e937523ee69ae25e18e372bf66b78d
00:15:24.503 [ForkJoinPool.commonPool-worker-51] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用化验岗位管理,注册模型：化验岗位表单
00:15:24.503 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/itemType/del already exists.
00:15:24.503 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料类型,注册接口：6bdbe3b6fd6be393a706bfdfa4ef2354
00:15:24.503 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用检测方法管理,注册接口：8e85f40cf2874a2faf96054e0ff30184
00:15:24.504 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/item/classParameterIncludeListInEdit already exists.
00:15:24.504 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/class/state already exists.
00:15:24.504 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：c6f91a147b83a49de951b962d4a8e951
00:15:24.504 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：e2e1b1e76617abdd9498f0155c3da075
00:15:24.505 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/itemParameterClass/page already exists.
00:15:24.505 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：3f425f0e8c1ca9d86141f19ec0eabbe5
00:15:24.505 [ForkJoinPool.commonPool-worker-27] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用检测方法管理,注册模型：检测方法管理表单
00:15:24.505 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用用来测试的,注册流程：null
00:15:24.507 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/parameter/save already exists.
00:15:24.507 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性,注册接口：239cceea250bbc77fad248fd9b321a37
00:15:24.508 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：班组库存日志表单
00:15:24.508 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/item/recoveryItem already exists.
00:15:24.508 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/itemType/itemClassInItemType already exists.
00:15:24.508 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/class/delClass already exists.
00:15:24.508 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：c4c495fab23665d153e999f51f161452
00:15:24.508 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：935b9b19acb0175eadfca0a23d3a6909
00:15:24.508 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料类型,注册接口：cd19102a77fe930769ad0c148703f70b
00:15:24.508 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/itemParameterClass/page2 already exists.
00:15:24.508 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：a73f7685677bc9a180e539cade24d710
00:15:24.509 [ForkJoinPool.commonPool-worker-21] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用设备台账,注册模型：设备台账表单
00:15:24.510 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/parameter/add already exists.
00:15:24.510 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性,注册接口：96e44ef16b852c12efc58fcdaa62d4ae
00:15:24.510 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料类型,注册接口：5d054a3df10858dea2aff2d5373dfaab
00:15:24.511 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用用来测试的,注册触发器：测试流程的提交、结束
00:15:24.511 [ForkJoinPool.commonPool-worker-38] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用用来测试的,注册触发器：数据模型
00:15:24.512 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/item/state already exists.
00:15:24.513 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：0e1ae946dfeddaeb2a416b824d333d9e
00:15:24.513 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/itemParameterClass/tree already exists.
00:15:24.513 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：500d252066f6887e37efa43dbc6bf0e6
00:15:24.514 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/class/classTree already exists.
00:15:24.514 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：5261a8eb9cb41edc457fb879f8bf61b2
00:15:24.514 [ForkJoinPool.commonPool-worker-8] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/parameter/update already exists.
00:15:24.514 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/itemType/itemTypeSelect already exists.
00:15:24.515 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料类型,注册接口：b95ccfbf51db231041ab9c6b370d1356
00:15:24.516 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/item/delItem already exists.
00:15:24.516 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/itemParameterClass/add already exists.
00:15:24.516 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：d288a1af4e169e6afcee3b452dad230c
00:15:24.517 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：1f2b93aca1250db17037c09c112b4de4
00:15:24.517 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/class/classParameterIncludeList already exists.
00:15:24.517 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/itemType/add already exists.
00:15:24.518 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：d0114ca7f7580a6a490b6ac903bb3f52
00:15:24.518 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料类型,注册接口：60c75f4fd2fc54d74fe723b2aba2caa0
00:15:24.519 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品预警查询PC
00:15:24.519 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料类型表单
00:15:24.519 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/item/classParameterIncludeListUnEdit already exists.
00:15:24.519 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/itemParameterClass/saveOne already exists.
00:15:24.520 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：9cc0911eae1fcc4c11e6f614e87eeadc
00:15:24.520 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料属性分类,注册接口：a3b4ab7250e5236fc1f88ee5c3d8a10a
00:15:24.520 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/class/page already exists.
00:15:24.520 [ForkJoinPool.commonPool-worker-7] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/itemType/update already exists.
00:15:24.520 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：bdbb6e44e710cacb0f152d99c9abf421
00:15:24.520 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：交接班日志统计PC
00:15:24.521 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品入库登记表单
00:15:24.522 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/itemParameterClass/update already exists.
00:15:24.522 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：库存预警
00:15:24.523 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/class/selectPage already exists.
00:15:24.523 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/item/itemPage already exists.
00:15:24.523 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：f29a40fec98a8d20d0fb45a256131ce1
00:15:24.523 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：7d119321882ea2acd46e2f9ee3a12bee
00:15:24.525 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：出入库统计查询
00:15:24.526 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/class/select already exists.
00:15:24.526 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：3e47b8a9e702b43bba1d19cc2f8a50ac
00:15:24.527 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/item/selectPage already exists.
00:15:24.527 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：c15e76dd71e0b99b685e4f6c2cd458cc
00:15:24.528 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料盘点表单
00:15:24.529 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/class/treeHaveCodeAndName already exists.
00:15:24.530 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料分类,注册接口：8c60532731f8ed1597d6ff77be8208cc
00:15:24.530 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品领用登记表单
00:15:24.530 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/item/getItemListByParameterList already exists.
00:15:24.530 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：1d5b3ab52faf2c9971b749c252266d5f
00:15:24.531 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原合同清单,注册视图：测试流程
00:15:24.532 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用原合同清单,注册视图：简单流程
00:15:24.533 [ForkJoinPool.commonPool-worker-44] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /interfaces/masterData/itemManage/class/addClass already exists.
00:15:24.533 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/item/info already exists.
00:15:24.533 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：6668a35ad472190d37f179c6e45938b8
00:15:24.535 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料出库表单
00:15:24.536 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/item/getParameterListByClassOrType already exists.
00:15:24.536 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：69c4d5c70e37c6a018724b67835e5052
00:15:24.537 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品退库登记表单
00:15:24.538 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用原合同清单,注册接口：fd74760c62b5422bb8fc14ed8b2a7d97
00:15:24.538 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/item/classParameterIncludeList already exists.
00:15:24.538 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：c94e43886ce6392168c1e09ce58c7d87
00:15:24.540 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用原合同清单,注册接口：92f0e5c7611d87d6b4716cc9030fb600
00:15:24.541 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/item/addItem already exists.
00:15:24.541 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用物料管理,注册接口：4943f5e7238a28201f47a87d505fbfd2
00:15:24.541 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用物料仓管理,注册视图：物料入库表单
00:15:24.541 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用原合同清单,注册接口：8bb04886c36afc3cbaefbc9e3a498c99
00:15:24.543 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用原合同清单,注册接口：54bf871d11cf9ccf66cc8c0a5e38b08e
00:15:24.544 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.c.LdfDynamicPathHandler - [addDynamicPath,62] - 映射已存在，进行处理: /masterData/itemManage/item/updateItem already exists.
00:15:24.544 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用原合同清单,注册接口：bee4464c10d59a63f8e10caed219de40
00:15:24.545 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用原合同清单,注册接口：fdb16b38bdcc841f0c84a6263dff08f1
00:15:24.545 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品盘点登记表单
00:15:24.548 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用原合同清单,注册模型：子表
00:15:24.549 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用原合同清单,注册模型：原合同清单
00:15:24.550 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用原合同清单,注册模型：一个新的数据模型
00:15:24.551 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料仓管理,注册模型：物料信息表单
00:15:24.551 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用原合同清单,注册模型：测试流程
00:15:24.552 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：交接班管理表单
00:15:24.552 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料仓管理,注册模型：物料库存表单
00:15:24.552 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用原合同清单,注册模型：简单流程
00:15:24.553 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料仓管理,注册模型：物料预警表单
00:15:24.553 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用原合同清单,注册流程：null
00:15:24.554 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料仓管理,注册模型：库存日志表单
00:15:24.555 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料仓管理,注册模型：物料类型表单
00:15:24.556 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料仓管理,注册模型：盘点物料
00:15:24.557 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料仓管理,注册模型：物料盘点表单
00:15:24.558 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品作废表单
00:15:24.560 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料仓管理,注册模型：出库物料
00:15:24.562 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用原合同清单,注册流程：null
00:15:24.562 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料仓管理,注册模型：物料出库表单
00:15:24.564 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用危化品仓管理,注册视图：危化品作废列表
00:15:24.565 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用物料仓管理,注册模型：物料入库表单
00:15:24.565 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：000e6c8baa6133c57c3246555517d31a
00:15:24.566 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：4ef677855b85fda50e27525410bea550
00:15:24.567 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：909fca60387660598dc22188279b735f
00:15:24.568 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$9,223] - 注册应用物料仓管理,注册条码：物料信息
00:15:24.568 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：62d406a7be909d20608c27ccd825455a
00:15:24.569 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：ddffb336e2eff20b8e0dec3a8b361135
00:15:24.570 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：da2c8c02b09e7a02ed37120fd5b134a4
00:15:24.571 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：f8950a92b105c370db26dd13d48434e0
00:15:24.572 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：78cc693d4d743b40e90f896cfd66afbf
00:15:24.572 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：5b5f17d3e493b899719aa6a620080ed6
00:15:24.574 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：f1ac506907a8f93f88a9004730cc3a13
00:15:24.575 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：5df8a676b75df54886bd919b3be81736
00:15:24.576 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用原合同清单,注册触发器：原合同清单触发器新增
00:15:24.577 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$8,206] - 注册应用原合同清单,注册定时任务：测试1
00:15:24.577 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：763d3004e391d032529819ad4827cd3f
00:15:24.577 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$9,223] - 注册应用原合同清单,注册条码：asd
00:15:24.578 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：65346a500ff5217bed5dcb6f05955607
00:15:24.579 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：328c297aac636f0406ff70d1a330093c
00:15:24.581 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：77c97b3945ee891f785deb142fd54839
00:15:24.582 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：ca636588e27c0addb2e906c4b85a94f0
00:15:24.583 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：eb341a2dc1f79b24379a8a5615a3c33b
00:15:24.584 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：404bd78a57c3fb4c7e7d2d41159a24c4
00:15:24.585 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：7c77b4ddc0e6e594de6f34944a6d06b0
00:15:24.585 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：1c8d1d6a2d771c0d1f60e462bdbcddb2
00:15:24.586 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：6da8f4aac40992216bf35351ff278120
00:15:24.587 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：461c51e8c9468b41b651741dee4eb79a
00:15:24.589 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：5eb60f702213ce27629f06421e8b064d
00:15:24.590 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：60fccf6045063c63acee5cf87a23400c
00:15:24.591 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：5265c9192a88fc92c42dc5ce6edff7b7
00:15:24.592 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：327b4ab2b2d916f3acdf8f13835c89e8
00:15:24.594 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：9247bec38758804f9eef89a06673ebe2
00:15:24.595 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：f9878c30b18a5483b289ee2913d1968f
00:15:24.596 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：c5e649418d631fbdb613042db604551e
00:15:24.596 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用危化品仓管理,注册接口：a4dd07f52a9bf60abc0a6f742245a55e
00:15:24.598 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：危化品实时库存表单
00:15:24.599 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：班组当班库存表单
00:15:24.600 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：危化品库存日志表单
00:15:24.601 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：危化品信息表单
00:15:24.601 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：班组库存日志表单
00:15:24.602 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：危化品入库登记表单
00:15:24.604 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：危化品领用明细表单
00:15:24.606 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：出库明细
00:15:24.607 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：危化品领用登记表单
00:15:24.609 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：退库明细
00:15:24.611 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：入库明细
00:15:24.612 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：危化品退库登记表单
00:15:24.614 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：盘点试剂
00:15:24.615 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：危化品盘点登记表单
00:15:24.617 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：交班明细
00:15:24.618 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：接班明细
00:15:24.619 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：交接班管理表单
00:15:24.621 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：作废明细
00:15:24.623 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用危化品仓管理,注册模型：危化品作废表单
00:15:24.625 [main] INFO  c.l.i.c.LdfBusinessInfoConfig - [loadProductApplication,423] - 默认环境工作区应用加载完毕！
00:15:24.628 [main] INFO  c.l.i.c.LdfBusinessInfoConfig - [loadDevelopApplication,135] - 开始加载中冶瑞木系统工作区应用！
00:15:24.872 [main] INFO  c.a.d.p.DruidDataSource - [init,995] - {dataSource-4,ldf_server_zyrm} inited
00:15:24.921 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户zyrm-dev应用信息, 应用名称: 用来测试的 应用Id: 1e7fe90ba493e543740fa45825843aba
00:15:24.922 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户zyrm-dev应用信息, 应用名称: 报表测试20250409 应用Id: e2aa8b0ab3fd67750b1e65d16400454e
00:15:24.921 [main] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户zyrm-dev应用信息, 应用名称: 知识库测试20250620 应用Id: 7be9ff537c0a021eee37d051a6e6235b
00:15:24.921 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户zyrm-dev应用信息, 应用名称: word报表20250707 应用Id: b0fbf1f0494d383c42bf8fb982f940a7
00:15:24.921 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户zyrm-dev应用信息, 应用名称: 报表测试20250823 应用Id: 38032d8d748063e565b871dbe6db8f56
00:15:24.921 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户zyrm-dev应用信息, 应用名称: 报表测试20250421 应用Id: 7681069ac47756d8776c2febe40070f0
00:15:24.922 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：简单的流程
00:15:24.922 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用word报表20250707,注册视图：列表界面
00:15:24.922 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用报表测试20250409,注册视图：报表测试
00:15:24.922 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用知识库测试20250620,注册接口：9ceb57d9007745a9b2e5eaf4e3fb1e8f
00:15:24.922 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报表测试20250823,注册接口：08fda8a68bef4d88a5891203f8ffc93c
00:15:24.922 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用报表测试20250421,注册视图：a_test_0421_1
00:15:24.923 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：测试模型管理
00:15:24.923 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用word报表20250707,注册视图：word报表测试
00:15:24.923 [ForkJoinPool.commonPool-worker-5] INFO  c.l.i.LdfBusinessConfig - [lambda$register$10,235] - 注册应用报表测试20250823,注册报表：报表签名错位
00:15:24.924 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用报表测试20250409,注册视图：列表展示
00:15:24.924 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用报表测试20250421,注册视图：a_test_0421_1列表
00:15:24.924 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：学生表管理
00:15:24.924 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报表测试20250409,注册接口：cf3e6bf458a343cebecc786cb14bda8a
00:15:24.924 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用word报表20250707,注册视图：表格条码
00:15:24.924 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报表测试20250421,注册接口：9b6b7c7b46e6415d9ec95e68e2009a84
00:15:24.924 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：测试模型
00:15:24.924 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用word报表20250707,注册接口：b20f9d61177146079794d8ebf09edaa9
00:15:24.925 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报表测试20250409,注册接口：b9c4732de2de4383bd2f1c29809e0069
00:15:24.925 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报表测试20250421,注册接口：6e0b2e26da8e498f8ba2bc288c81d4c1
00:15:24.925 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用word报表20250707,注册模型：word报表测试
00:15:24.926 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报表测试20250409,注册接口：4fe3a97aa7a9417684f34c5549751ec1
00:15:24.926 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [lambda$register$9,223] - 注册应用word报表20250707,注册条码：表格测试
00:15:24.927 [ForkJoinPool.commonPool-worker-48] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用报表测试20250421,注册模型：a_test_0421_1
00:15:24.927 [ForkJoinPool.commonPool-worker-36] INFO  c.l.i.LdfBusinessConfig - [lambda$register$10,235] - 注册应用word报表20250707,注册报表：word1
00:15:24.928 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报表测试20250409,注册接口：232ca5e1ce5f49459fdd106f6b5cc069
00:15:24.928 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：学生表
00:15:24.928 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报表测试20250409,注册接口：44265f486c4b4040b3f8bbd6efcb5457
00:15:24.929 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报表测试20250409,注册接口：0525e3f0400d4008888266393d628869
00:15:24.930 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报表测试20250409,注册接口：3dd4c5323dfc4c538c0832ef6a9796ae
00:15:24.931 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用报表测试20250409,注册模型：报表测试
00:15:24.931 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$10,235] - 注册应用报表测试20250409,注册报表：test0509-1
00:15:24.932 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：用户列表
00:15:24.932 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$10,235] - 注册应用报表测试20250409,注册报表：test0509-2
00:15:24.932 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：222
00:15:24.932 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$10,235] - 注册应用报表测试20250409,注册报表：test0509-3
00:15:24.933 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：简单的流程
00:15:24.933 [ForkJoinPool.commonPool-worker-59] INFO  c.l.i.LdfBusinessConfig - [lambda$register$10,235] - 注册应用报表测试20250409,注册报表：0512
00:15:24.937 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：test_model_x
00:15:24.939 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：数据管理
00:15:24.940 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：规则引擎
00:15:24.940 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用用来测试的,注册视图：测试条码中的表格
00:15:24.941 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用用来测试的,注册接口：5478b2a26725469390dc033d367359df
00:15:24.942 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用用来测试的,注册接口：3b8502d33e9a4adfb4e16bdc4a2077a6
00:15:24.943 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用用来测试的,注册接口：ff2b1b5c1a5f4808b0cf28caba53bfc6
00:15:24.944 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用用来测试的,注册接口：82960643a77f4b67870d73b9707afd00
00:15:24.954 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用用来测试的,注册接口：2a0259c9dfc14db0bae6bb23b9bd2808
00:15:24.954 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用用来测试的,注册接口：baf9f8d9a8f4465db13d331b1de69a6d
00:15:24.955 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用用来测试的,注册接口：a2bda6ff50894a1298f9b48a7c4f0ef9
00:15:24.956 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用用来测试的,注册接口：5b83a8bf0c6e4455be6238a89e87f4e7
00:15:24.957 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用用来测试的,注册模型：测试模型
00:15:24.957 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用用来测试的,注册模型：data-table
00:15:24.958 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用用来测试的,注册模型：学生表
00:15:24.959 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用用来测试的,注册模型：子表
00:15:24.959 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用用来测试的,注册模型：简单的流程
00:15:24.960 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用用来测试的,注册模型：test_model_x
00:15:24.961 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$6,181] - 注册应用用来测试的,注册流程：null
00:15:24.963 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用用来测试的,注册触发器：测试流程的提交、结束
00:15:24.963 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$7,194] - 注册应用用来测试的,注册触发器：数据模型
00:15:24.964 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$8,206] - 注册应用用来测试的,注册定时任务：md测试
00:15:24.964 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$9,223] - 注册应用用来测试的,注册条码：测试条码
00:15:24.965 [ForkJoinPool.commonPool-worker-58] INFO  c.l.i.LdfBusinessConfig - [lambda$register$9,223] - 注册应用用来测试的,注册条码：测试条码中的表格
00:15:24.965 [main] INFO  c.l.i.c.LdfBusinessInfoConfig - [loadDevelopApplication,275] - 中冶瑞木系统工作区应用加载完毕！
00:15:24.965 [main] INFO  c.l.i.c.LdfBusinessInfoConfig - [loadProductApplication,283] - 加载zyrm环境应用
00:15:25.269 [main] INFO  c.a.d.p.DruidDataSource - [init,995] - {dataSource-5,ldf_server_zyrm} inited
00:15:25.291 [main] INFO  c.l.i.LdfBusinessConfig - [register,132] - 系统加载租户zyrm应用信息, 应用名称: 报表测试20250409 应用Id: e2aa8b0ab3fd67750b1e65d16400454e
00:15:25.291 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用报表测试20250409,注册视图：报表测试
00:15:25.292 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$3,140] - 注册应用报表测试20250409,注册视图：列表展示
00:15:25.293 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$4,154] - 注册应用报表测试20250409,注册接口：cf3e6bf458a343cebecc786cb14bda8a
00:15:25.294 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$5,168] - 注册应用报表测试20250409,注册模型：报表测试
00:15:25.295 [main] INFO  c.l.i.LdfBusinessConfig - [lambda$register$9,223] - 注册应用报表测试20250409,注册条码：111
00:15:25.295 [main] INFO  c.l.i.c.LdfBusinessInfoConfig - [loadProductApplication,423] - 中冶瑞木系统工作区应用加载完毕！
00:15:25.295 [main] INFO  c.l.i.c.LdfBusinessInfoConfig - [init,99] - 加载租户应用数据完成，用时：4444 ms
00:15:25.377 [main] INFO  c.l.i.t.BackupDataCleanTask - [lambda$configureTasks$1,36] - 定时清理备份数据cron表达式：0 0 0 ? * *
00:15:25.383 [main] INFO  c.l.i.LogicTrueInterfacesApplication - [logStarted,61] - Started LogicTrueInterfacesApplication in 46.91 seconds (JVM running for 48.338)
00:15:25.417 [main] INFO  c.l.i.i.c.s.i.DefaultMagicResourceService - [lambda$null$6,189] - 加载接口默认数据源
00:15:25.441 [main] INFO  c.l.i.i.c.s.i.DefaultMagicResourceService - [lambda$null$6,189] - 加载接口energy-dev
00:15:25.442 [main] INFO  c.l.i.i.c.s.i.DefaultMagicResourceService - [lambda$null$6,189] - 加载接口lims-dev
00:15:25.443 [main] INFO  c.l.i.i.c.s.i.DefaultMagicResourceService - [lambda$null$6,189] - 加载接口默认数据源-dev
00:15:25.444 [main] INFO  c.l.i.i.c.s.i.DefaultMagicResourceService - [lambda$null$6,189] - 加载接口默认数据源
00:15:25.445 [main] INFO  c.l.i.i.c.s.i.DefaultMagicResourceService - [lambda$null$6,189] - 加载接口默认数据源
00:15:25.462 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-interfaces-dev.yml+DEFAULT_GROUP+dev
00:15:25.463 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-interfaces-dev.yml, group=DEFAULT_GROUP, cnt=1
00:15:25.463 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-interfaces.yml+DEFAULT_GROUP+dev
00:15:25.464 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-interfaces.yml, group=DEFAULT_GROUP, cnt=1
00:15:25.464 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-interfaces+DEFAULT_GROUP+dev
00:15:25.464 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-interfaces, group=DEFAULT_GROUP, cnt=1
00:15:25.866 [RMI TCP Connection(1)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:16:51.866 [asyncExecutor-1] INFO  o.a.d.i.File2Stream - [info,106] - path to stream library/ambiguity.dic
00:16:51.868 [asyncExecutor-1] INFO  o.a.d.i.File2Stream - [info,106] - path to stream library/default.dic
00:16:52.426 [asyncExecutor-1] INFO  o.a.l.DATDictionary - [info,106] - init core library ok use time : 499
00:16:52.751 [asyncExecutor-1] INFO  o.a.l.NgramLibrary - [info,106] - init ngram ok use time :275
00:16:53.768 [pool-7-thread-1] INFO  c.l.i.i.d.m.MagicDynamicDataSource - [put,73] - 注册数据源：zyrm-dev
00:21:59.979 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,90] - De-registering from Nacos Server now...
00:21:59.980 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [deregisterService,136] - [DEREGISTER-SERVICE] dev deregistering service logictrue-interfaces with instance: Instance{instanceId='null', ip='**************', port=9210, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
00:21:59.983 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,110] - De-registration finished.
00:21:59.986 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,256] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
00:21:59.986 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,140] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
00:21:59.987 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,142] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
00:21:59.987 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,258] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
00:21:59.987 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,176] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
00:21:59.987 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,130] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
00:22:00.289 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,132] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
00:22:00.291 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,188] - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
00:22:00.292 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,193] - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
00:22:00.292 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,519] - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
00:22:00.292 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,162] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
00:22:00.293 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,164] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
00:22:00.293 [SpringApplicationShutdownHook] INFO  c.a.n.c.i.CredentialWatcher - [stop,105] - [null] CredentialWatcher is stopped
00:22:00.293 [SpringApplicationShutdownHook] INFO  c.a.n.c.i.CredentialService - [free,99] - [null] CredentialService is freed
00:22:00.293 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,523] - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
00:22:00.293 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,461] - Shutdown rpc client ,set status to shutdown
00:22:00.293 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,463] - Shutdown  client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5f4dcd40[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
00:22:00.294 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,465] - Close current connection 1755879285493_172.21.0.1_56250
00:22:00.302 [nacos-grpc-client-executor-101] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755879285493_172.21.0.1_56250]Ignore complete event,isRunning:false,isAbandon=false
00:22:00.307 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,83] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@73b7f3f7[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 102]
00:22:00.308 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,251] - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@304d4e1e[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 140]
00:22:00.308 [SpringApplicationShutdownHook] INFO  c.a.n.client.naming - [shutdown,182] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
00:22:00.421 [SpringApplicationShutdownHook] INFO  c.l.i.u.ElasticSearchClient - [close,133] - Elasticsearch客户端已关闭
00:22:00.423 [SpringApplicationShutdownHook] INFO  o.a.i.s.p.SessionPool - [close,568] - closing the session pool, cleaning queues...
00:22:00.466 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2073] - {dataSource-1} closing ...
00:22:00.477 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2146] - {dataSource-1} closed
00:22:00.701 [SpringApplicationShutdownHook] INFO  o.m.d.connection - [info,71] - Closed connection [connectionId{localValue:3, serverValue:49774}] to localhost:27017 because the pool has been closed.
